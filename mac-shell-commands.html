<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mac & Shell 命令行大全 - 专业开发者指南</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --dark-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            --accent-color: #6366f1;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --border-radius: 12px;
            --border-radius-lg: 16px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .background-pattern {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
            border-radius: 2px;
        }

        .header h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1rem;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            font-weight: 400;
            margin-bottom: 0.5rem;
        }

        .header .description {
            font-size: 1rem;
            opacity: 0.7;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 3rem;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-input {
            width: 100%;
            padding: 1rem 1.5rem 1rem 3.5rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1.1rem;
            background: var(--bg-primary);
            outline: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Inter', sans-serif;
            position: relative;
        }

        .search-input:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
            transform: translateY(-1px);
        }

        .search-icon {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .search-wrapper {
            position: relative;
        }

        .stats-bar {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-number {
            color: var(--accent-color);
            font-weight: 700;
        }

        .categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .category {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .category::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .category:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .category:hover::before {
            transform: scaleX(1);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--bg-tertiary);
        }

        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius);
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            letter-spacing: -0.01em;
        }

        .category-count {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: auto;
        }

        .command-item {
            margin-bottom: 1rem;
            padding: 1.5rem;
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .command-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--accent-color);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .command-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
            border-color: var(--accent-color);
        }

        .command-item:hover::before {
            transform: scaleY(1);
        }

        .command-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .command-name {
            font-family: 'JetBrains Mono', 'Monaco', 'Menlo', 'Consolas', monospace;
            font-weight: 600;
            color: var(--accent-color);
            font-size: 1.1rem;
            background: rgba(99, 102, 241, 0.1);
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        .command-type {
            font-size: 0.75rem;
            color: var(--text-secondary);
            background: var(--bg-tertiary);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .command-desc {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .command-example {
            font-family: 'JetBrains Mono', 'Monaco', 'Menlo', 'Consolas', monospace;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #e2e8f0;
            padding: 1rem 1.25rem;
            border-radius: var(--border-radius);
            font-size: 0.9rem;
            overflow-x: auto;
            position: relative;
            border: 1px solid #475569;
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .command-example::before {
            content: '$';
            color: var(--success-color);
            margin-right: 0.5rem;
            font-weight: 600;
        }

        .footer {
            text-align: center;
            margin-top: 4rem;
            padding: 2rem;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .footer-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .footer h3 {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .footer p {
            opacity: 0.8;
            line-height: 1.6;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @media (max-width: 1024px) {
            .categories {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2.5rem;
            }

            .categories {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .category {
                padding: 1.5rem;
            }

            .search-container {
                padding: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 2rem;
            }

            .command-item {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="background-pattern"></div>

    <div class="container">
        <div class="header">
            <h1>Mac & Shell 命令行大全</h1>
            <p class="subtitle">专业开发者终端指南</p>
            <p class="description">掌握这些命令，提升你的开发效率和系统管理能力</p>
            
        
        </div>

        <div class="search-container">
            <div class="search-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="搜索命令、描述或示例..." id="searchInput">
            </div>
            <div class="stats-bar">
                <div class="stat-item">
                    <i class="fas fa-terminal"></i>
                    <span>总计 <span class="stat-number" id="totalCommands">80+</span> 个命令</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-layer-group"></i>
                    <span><span class="stat-number">8</span> 个分类</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-code"></i>
                    <span>实用示例</span>
                </div>
            </div>
        </div>

        <div class="categories" id="categoriesContainer">
            <!-- 文件和目录操作 -->
            <div class="category fade-in">
                <div class="category-header">
                    <div class="category-icon">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="category-title">文件和目录操作</div>
                    <div class="category-count">6</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">ls</div>
                        <div class="command-type">基础</div>
                    </div>
                    <div class="command-desc">列出目录内容，显示文件和子目录信息</div>
                    <div class="command-example">ls -la</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">cd</div>
                        <div class="command-type">导航</div>
                    </div>
                    <div class="command-desc">切换当前工作目录到指定路径</div>
                    <div class="command-example">cd ~/Documents</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">pwd</div>
                        <div class="command-type">信息</div>
                    </div>
                    <div class="command-desc">显示当前工作目录的完整路径</div>
                    <div class="command-example">pwd</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">mkdir</div>
                        <div class="command-type">创建</div>
                    </div>
                    <div class="command-desc">创建新目录，-p 参数可创建多级目录</div>
                    <div class="command-example">mkdir -p new/folder/structure</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">rmdir</div>
                        <div class="command-type">删除</div>
                    </div>
                    <div class="command-desc">删除空目录，目录必须为空才能删除</div>
                    <div class="command-example">rmdir empty_folder</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">rm</div>
                        <div class="command-type">删除</div>
                    </div>
                    <div class="command-desc">删除文件或目录，-rf 强制递归删除</div>
                    <div class="command-example">rm -rf folder</div>
                </div>
            </div>

            <!-- 文件操作 -->
            <div class="category fade-in">
                <div class="category-header">
                    <div class="category-icon">
                        <i class="fas fa-file"></i>
                    </div>
                    <div class="category-title">文件操作</div>
                    <div class="category-count">5</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">cp</div>
                        <div class="command-type">复制</div>
                    </div>
                    <div class="command-desc">复制文件或目录，-r 参数用于递归复制目录</div>
                    <div class="command-example">cp -r source_folder destination_folder</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">mv</div>
                        <div class="command-type">移动</div>
                    </div>
                    <div class="command-desc">移动文件/目录到新位置，或重命名文件</div>
                    <div class="command-example">mv old_name.txt new_name.txt</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">touch</div>
                        <div class="command-type">创建</div>
                    </div>
                    <div class="command-desc">创建空文件或更新现有文件的时间戳</div>
                    <div class="command-example">touch newfile.txt</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">find</div>
                        <div class="command-type">搜索</div>
                    </div>
                    <div class="command-desc">在目录树中搜索文件和目录，支持多种条件</div>
                    <div class="command-example">find . -name "*.txt" -type f</div>
                </div>

                <div class="command-item">
                    <div class="command-header">
                        <div class="command-name">locate</div>
                        <div class="command-type">搜索</div>
                    </div>
                    <div class="command-desc">基于数据库快速查找文件，比find更快</div>
                    <div class="command-example">locate filename</div>
                </div>
            </div>

            <!-- 文本处理 -->
            <div class="category">
                <h2 class="category-title">
                    <span class="category-icon">📝</span>
                    文本处理
                </h2>
                <div class="command-item">
                    <div class="command-name">cat</div>
                    <div class="command-desc">显示文件内容</div>
                    <div class="command-example">cat file.txt</div>
                </div>
                <div class="command-item">
                    <div class="command-name">less</div>
                    <div class="command-desc">分页显示文件内容</div>
                    <div class="command-example">less file.txt</div>
                </div>
                <div class="command-item">
                    <div class="command-name">head</div>
                    <div class="command-desc">显示文件开头几行</div>
                    <div class="command-example">head -n 10 file.txt</div>
                </div>
                <div class="command-item">
                    <div class="command-name">tail</div>
                    <div class="command-desc">显示文件末尾几行</div>
                    <div class="command-example">tail -f log.txt</div>
                </div>
                <div class="command-item">
                    <div class="command-name">grep</div>
                    <div class="command-desc">搜索文本模式</div>
                    <div class="command-example">grep -r "pattern" .</div>
                </div>
                <div class="command-item">
                    <div class="command-name">sed</div>
                    <div class="command-desc">流编辑器，替换文本</div>
                    <div class="command-example">sed 's/old/new/g' file.txt</div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="category">
                <h2 class="category-title">
                    <span class="category-icon">💻</span>
                    系统信息
                </h2>
                <div class="command-item">
                    <div class="command-name">ps</div>
                    <div class="command-desc">显示运行中的进程</div>
                    <div class="command-example">ps aux</div>
                </div>
                <div class="command-item">
                    <div class="command-name">top</div>
                    <div class="command-desc">实时显示系统进程</div>
                    <div class="command-example">top</div>
                </div>
                <div class="command-item">
                    <div class="command-name">htop</div>
                    <div class="command-desc">增强版的top命令</div>
                    <div class="command-example">htop</div>
                </div>
                <div class="command-item">
                    <div class="command-name">df</div>
                    <div class="command-desc">显示磁盘空间使用情况</div>
                    <div class="command-example">df -h</div>
                </div>
                <div class="command-item">
                    <div class="command-name">du</div>
                    <div class="command-desc">显示目录大小</div>
                    <div class="command-example">du -sh *</div>
                </div>
                <div class="command-item">
                    <div class="command-name">free</div>
                    <div class="command-desc">显示内存使用情况</div>
                    <div class="command-example">free -h</div>
                </div>
            </div>

            <!-- 网络命令 -->
            <div class="category">
                <h2 class="category-title">
                    <span class="category-icon">🌐</span>
                    网络命令
                </h2>
                <div class="command-item">
                    <div class="command-name">ping</div>
                    <div class="command-desc">测试网络连接</div>
                    <div class="command-example">ping google.com</div>
                </div>
                <div class="command-item">
                    <div class="command-name">curl</div>
                    <div class="command-desc">下载文件或发送HTTP请求</div>
                    <div class="command-example">curl -O https://example.com/file.zip</div>
                </div>
                <div class="command-item">
                    <div class="command-name">wget</div>
                    <div class="command-desc">下载文件</div>
                    <div class="command-example">wget https://example.com/file.zip</div>
                </div>
                <div class="command-item">
                    <div class="command-name">ssh</div>
                    <div class="command-desc">远程登录</div>
                    <div class="command-example">ssh user@hostname</div>
                </div>
                <div class="command-item">
                    <div class="command-name">scp</div>
                    <div class="command-desc">远程复制文件</div>
                    <div class="command-example">scp file.txt user@host:/path/</div>
                </div>
                <div class="command-item">
                    <div class="command-name">netstat</div>
                    <div class="command-desc">显示网络连接</div>
                    <div class="command-example">netstat -tuln</div>
                </div>
            </div>

            <!-- 压缩和归档 -->
            <div class="category">
                <h2 class="category-title">
                    <span class="category-icon">🗜️</span>
                    压缩和归档
                </h2>
                <div class="command-item">
                    <div class="command-name">tar</div>
                    <div class="command-desc">创建和提取归档文件</div>
                    <div class="command-example">tar -czf archive.tar.gz folder/</div>
                </div>
                <div class="command-item">
                    <div class="command-name">zip</div>
                    <div class="command-desc">创建ZIP压缩文件</div>
                    <div class="command-example">zip -r archive.zip folder/</div>
                </div>
                <div class="command-item">
                    <div class="command-name">unzip</div>
                    <div class="command-desc">解压ZIP文件</div>
                    <div class="command-example">unzip archive.zip</div>
                </div>
                <div class="command-item">
                    <div class="command-name">gzip</div>
                    <div class="command-desc">压缩文件</div>
                    <div class="command-example">gzip file.txt</div>
                </div>
                <div class="command-item">
                    <div class="command-name">gunzip</div>
                    <div class="command-desc">解压gzip文件</div>
                    <div class="command-example">gunzip file.txt.gz</div>
                </div>
            </div>

            <!-- Mac特有命令 -->
            <div class="category">
                <h2 class="category-title">
                    <span class="category-icon">🍎</span>
                    Mac特有命令
                </h2>
                <div class="command-item">
                    <div class="command-name">open</div>
                    <div class="command-desc">打开文件或应用程序</div>
                    <div class="command-example">open -a "Visual Studio Code" .</div>
                </div>
                <div class="command-item">
                    <div class="command-name">pbcopy</div>
                    <div class="command-desc">复制到剪贴板</div>
                    <div class="command-example">cat file.txt | pbcopy</div>
                </div>
                <div class="command-item">
                    <div class="command-name">pbpaste</div>
                    <div class="command-desc">从剪贴板粘贴</div>
                    <div class="command-example">pbpaste > file.txt</div>
                </div>
                <div class="command-item">
                    <div class="command-name">say</div>
                    <div class="command-desc">文本转语音</div>
                    <div class="command-example">say "Hello World"</div>
                </div>
                <div class="command-item">
                    <div class="command-name">screencapture</div>
                    <div class="command-desc">截屏</div>
                    <div class="command-example">screencapture -s screenshot.png</div>
                </div>
                <div class="command-item">
                    <div class="command-name">brew</div>
                    <div class="command-desc">Homebrew包管理器</div>
                    <div class="command-example">brew install git</div>
                </div>
            </div>

            <!-- 权限和用户 -->
            <div class="category">
                <h2 class="category-title">
                    <span class="category-icon">🔐</span>
                    权限和用户
                </h2>
                <div class="command-item">
                    <div class="command-name">chmod</div>
                    <div class="command-desc">修改文件权限</div>
                    <div class="command-example">chmod 755 script.sh</div>
                </div>
                <div class="command-item">
                    <div class="command-name">chown</div>
                    <div class="command-desc">修改文件所有者</div>
                    <div class="command-example">chown user:group file.txt</div>
                </div>
                <div class="command-item">
                    <div class="command-name">sudo</div>
                    <div class="command-desc">以管理员权限执行命令</div>
                    <div class="command-example">sudo apt update</div>
                </div>
                <div class="command-item">
                    <div class="command-name">su</div>
                    <div class="command-desc">切换用户</div>
                    <div class="command-example">su - username</div>
                </div>
                <div class="command-item">
                    <div class="command-name">whoami</div>
                    <div class="command-desc">显示当前用户名</div>
                    <div class="command-example">whoami</div>
                </div>
                <div class="command-item">
                    <div class="command-name">id</div>
                    <div class="command-desc">显示用户和组ID</div>
                    <div class="command-example">id</div>
                </div>
            </div>

            <!-- 环境变量和别名 -->
            <div class="category">
                <h2 class="category-title">
                    <span class="category-icon">⚙️</span>
                    环境变量和别名
                </h2>
                <div class="command-item">
                    <div class="command-name">export</div>
                    <div class="command-desc">设置环境变量</div>
                    <div class="command-example">export PATH=$PATH:/new/path</div>
                </div>
                <div class="command-item">
                    <div class="command-name">env</div>
                    <div class="command-desc">显示所有环境变量</div>
                    <div class="command-example">env</div>
                </div>
                <div class="command-item">
                    <div class="command-name">alias</div>
                    <div class="command-desc">创建命令别名</div>
                    <div class="command-example">alias ll='ls -la'</div>
                </div>
                <div class="command-item">
                    <div class="command-name">unalias</div>
                    <div class="command-desc">删除别名</div>
                    <div class="command-example">unalias ll</div>
                </div>
                <div class="command-item">
                    <div class="command-name">which</div>
                    <div class="command-desc">查找命令位置</div>
                    <div class="command-example">which python</div>
                </div>
                <div class="command-item">
                    <div class="command-name">type</div>
                    <div class="command-desc">显示命令类型</div>
                    <div class="command-example">type ls</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <div class="footer-content">
                <h3><i class="fas fa-lightbulb"></i> 专业提示</h3>
                <p>使用 <code>man 命令名</code> 查看详细帮助文档，例如：<code>man ls</code></p>
                <p>使用 <code>命令名 --help</code> 查看快速帮助信息</p>
                <p>按 <code>Tab</code> 键可以自动补全命令和文件名</p>
            </div>
        </div>
    </div>

    <script>
        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const categories = document.querySelectorAll('.category');
            
            categories.forEach(category => {
                const commands = category.querySelectorAll('.command-item');
                let hasVisibleCommand = false;
                
                commands.forEach(command => {
                    const commandName = command.querySelector('.command-name').textContent.toLowerCase();
                    const commandDesc = command.querySelector('.command-desc').textContent.toLowerCase();
                    
                    if (commandName.includes(searchTerm) || commandDesc.includes(searchTerm)) {
                        command.style.display = 'block';
                        hasVisibleCommand = true;
                    } else {
                        command.style.display = 'none';
                    }
                });
                
                category.style.display = hasVisibleCommand ? 'block' : 'none';
            });
        });
    </script>
</body>
</html>
