<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor付费凭证生成器</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: bold;
        }
        
        input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        input[type="email"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎫 Cursor付费凭证生成器</h1>
        
        <div class="warning">
            ⚠️ 仅供学习和测试使用，请勿用于非法用途！
        </div>
        
        <div class="form-group">
            <label for="email">邮箱地址：</label>
            <input type="email" id="email" placeholder="输入您的邮箱地址" value="<EMAIL>">
        </div>
        
        <button class="generate-btn" onclick="generateReceipt()">
            🚀 生成付费凭证
        </button>
        
        <div class="info">
            💡 点击按钮后会在新窗口中打开生成的付费凭证，您可以打印或保存为PDF。
        </div>
    </div>

    <script>
        // 生成随机Invoice号码
        function generateInvoiceNumber() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 8; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            const numbers = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            return result + '-' + numbers;
        }

        // 生成随机收据号码
        function generateReceiptNumber() {
            const part1 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            const part2 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            return part1 + '-' + part2;
        }

        // 生成随机支付方式
        function generatePaymentMethod() {
            const cardTypes = ['Visa', 'MasterCard', 'American Express', 'Discover'];
            const lastFourDigits = ['1234', '5678', '9012', '3456', '7890', '2468', '1357', '8024'];
            
            const randomCardType = cardTypes[Math.floor(Math.random() * cardTypes.length)];
            const randomLastFour = lastFourDigits[Math.floor(Math.random() * lastFourDigits.length)];
            
            return randomCardType + ' - ' + randomLastFour;
        }

        // 生成随机日期
        function generateRandomDate() {
            const today = new Date();
            const fifteenDaysAgo = new Date(today);
            fifteenDaysAgo.setDate(today.getDate() - 15);
            
            const timeDiff = today.getTime() - fifteenDaysAgo.getTime();
            const randomTime = Math.random() * timeDiff;
            const randomDate = new Date(fifteenDaysAgo.getTime() + randomTime);
            
            return randomDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // 生成随机收票人信息
        function generateBillToInfo(email) {
            const firstNames = ['JAMES', 'JOHN', 'ROBERT', 'MICHAEL', 'WILLIAM', 'MARY', 'PATRICIA', 'JENNIFER'];
            const lastNames = ['SMITH', 'JOHNSON', 'WILLIAMS', 'BROWN', 'JONES', 'GARCIA', 'MILLER', 'DAVIS'];
            const addresses = [
                '2017 North Hartford Drive',
                '1425 West Maple Street', 
                '3892 East Oak Avenue',
                '756 South Pine Road'
            ];
            const cities = [
                'Los Angeles, California 90210',
                'New York, New York 10001',
                'Chicago, Illinois 60601',
                'Houston, Texas 77002'
            ];
            
            const randomFirstName = firstNames[Math.floor(Math.random() * firstNames.length)];
            const randomLastName = lastNames[Math.floor(Math.random() * lastNames.length)];
            const randomAddress = addresses[Math.floor(Math.random() * addresses.length)];
            const randomCity = cities[Math.floor(Math.random() * cities.length)];
            
            return {
                name: randomFirstName + ' ' + randomLastName,
                address1: randomAddress,
                address2: randomCity,
                country: 'United States',
                email: email
            };
        }

        // 生成日期范围
        function generateDateRange(datePaid) {
            const paidDate = new Date(datePaid);
            const startDate = new Date(paidDate);
            const endDate = new Date(paidDate);
            endDate.setMonth(endDate.getMonth() + 1);
            
            const formatDate = (date) => {
                return date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric'
                });
            };
            
            return formatDate(startDate) + ' - ' + formatDate(endDate) + ', ' + paidDate.getFullYear();
        }

        // 生成随机Invoice数据
        function generateRandomInvoice(email) {
            const invoiceNumber = generateInvoiceNumber();
            const receiptNumber = generateReceiptNumber();
            const datePaid = generateRandomDate();
            const paymentMethod = generatePaymentMethod();
            const billTo = generateBillToInfo(email);
            const amount = '$20.00';
            const description = 'Cursor Pro';
            const dateRange = generateDateRange(datePaid);

            return {
                invoiceNumber: invoiceNumber,
                receiptNumber: receiptNumber,
                datePaid: datePaid,
                paymentMethod: paymentMethod,
                billTo: billTo,
                amount: amount,
                description: description,
                dateRange: dateRange
            };
        }

        // 主要生成函数
        window.generateReceipt = function() {
            const email = document.getElementById('email').value;
            if (!email) {
                alert('请输入邮箱地址！');
                return;
            }
            
            const invoiceData = generateRandomInvoice(email);
            const htmlContent = createInvoiceHTML(invoiceData);
            
            // 在新窗口中打开生成的发票
            const newWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            newWindow.document.write(htmlContent);
            newWindow.document.close();
        };

        // 创建发票HTML - 使用字符串拼接避免模板字符串问题
        function createInvoiceHTML(data) {
            var html = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8">';
            html += '<title>Receipt - ' + data.invoiceNumber + '</title>';
            html += '<style>';
            html += 'body{font-family:\'Inter\',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Helvetica,Arial,sans-serif;margin:40px;background:#f5f5f5}';
            html += '.receipt{background:white;padding:40px;max-width:800px;margin:0 auto;box-shadow:0 0 10px rgba(0,0,0,0.1)}';
            html += '.header{display:flex;justify-content:space-between;margin-bottom:30px;border-bottom:2px solid #000;padding-bottom:20px}';
            html += '.header h1{font-size:28px;font-weight:700;margin:0}';
            html += '.logo{display:flex;align-items:center;gap:12px;justify-content:flex-end;margin-right:0px}.logo img{width:auto;height:80px}';
            html += '.invoice-info{margin-bottom:30px}.invoice-info table{border-collapse:collapse}';
            html += '.invoice-info td{padding:8px 30px 8px 0;font-size:13px;vertical-align:top;line-height:1.6}';
            html += '.address-section{display:flex;gap:120px;margin-bottom:20px;font-size:13px}';
            html += '.address p{margin:3px 0}';
            html += '.summary{margin-bottom:40px}';
            html += '.summary h2{font-size:16px;font-weight:700;margin:0}';
            html += '.items-table{width:100%;border-collapse:collapse;font-size:13px}';
            html += '.items-table thead th{text-align:right;border-bottom:1px solid #9ca3af;padding-bottom:8px;font-weight:400;color:#000000;font-size:12px}';
            html += '.items-table thead th:first-child{text-align:left}';
            html += '.items-table th:nth-child(2),.items-table td:nth-child(2){width:15%}';
            html += '.items-table th:nth-child(3),.items-table td:nth-child(3){width:15%}';
            html += '.items-table th:nth-child(4),.items-table td:nth-child(4){width:13%}';
            html += '.items-table td{text-align:left}';
            html += '.items-table td:nth-child(2),.items-table td:nth-child(3),.items-table td:nth-child(4){text-align:right}';
            html += '.item-description{color:#000000}';
            html += '.item-date{font-size:12px;margin-top:2px}';
            html += '.totals-section{display:flex;justify-content:flex-end}';
            html += '.totals-table{width:350px;font-size:13px}';
            html += '.totals-table td{padding:1px 0}';
            html += '.totals-table td:last-child{text-align:right;font-weight:500}';
            html += '.totals-table tr td{border-top:1px solid #e5e7eb}';
            html += '.totals-table tr:last-child td{font-weight:700}';
            html += '.footer{margin-top:auto;padding-top:15px;border-top:1px solid #e5e7eb;display:flex;justify-content:space-between;font-size:11px}';
            html += '@media print{body{margin:0;background:white}.receipt{box-shadow:none}}';
            html += '</style></head><body>';
            html += '<div class="receipt">';
            html += '<div class="header"><h1>Receipt</h1><div class="logo"><img src="https://liubao.org.cn/image/cursor.png" alt="Cursor Logo" /></div></div>';
            html += '<div class="invoice-info"><table>';
            html += '<tr><td><b>Invoice number:</b></td><td><b>' + data.invoiceNumber + '</b></td></tr>';
            html += '<tr><td><b>Receipt number:</b></td><td><b>' + data.receiptNumber + '</b></td></tr>';
            html += '<tr><td><b>Date paid:</b></td><td><b>' + data.datePaid + '</b></td></tr>';
            html += '<tr><td><b>Payment method:</b></td><td><b>' + data.paymentMethod + '</b></td></tr>';
            html += '</table></div>';
            html += '<div class="address-section">';
            html += '<div><p><b>Cursor AI Inc.</b></p><p>548 Market St PMB 35410</p>';
            html += '<p>San Francisco, CA 94104-5401</p><p>United States</p>';
            html += '<p><EMAIL></p><p>US EIN 88-1234567</p></div>';
            html += '<div><p><b>Bill to:</b></p><p>' + data.billTo.name + '</p>';
            html += '<p>' + data.billTo.address1 + '</p><p>' + data.billTo.address2 + '</p>';
            html += '<p>' + data.billTo.country + '</p><p>' + data.billTo.email + '</p></div>';
            html += '</div>';
            html += '<div class="summary"><h2>' + data.amount + ' paid on ' + data.datePaid + '</h2></div>';
            html += '<table class="items-table">';
            html += '<thead><tr><th>Description</th><th>Qty</th><th>Unit price</th><th>Amount</th></tr></thead>';
            html += '<tbody><tr><td><div class="item-description">' + data.description + '</div>';
            html += '<div class="item-date">' + data.dateRange + '</div></td>';
            html += '<td>1</td><td>' + data.amount + '</td><td><b>' + data.amount + '</b></td></tr></tbody>';
            html += '</table>';
            html += '<div class="totals-section">';
            html += '<table class="totals-table">';
            html += '<tbody>';
            html += '<tr><td>Subtotal</td><td>' + data.amount + '</td></tr>';
            html += '<tr><td>Total</td><td>' + data.amount + '</td></tr>';
            html += '<tr><td><b>Amount paid</b></td><td><b>' + data.amount + '</b></td></tr>';
            html += '</tbody>';
            html += '</table></div>';
            html += '<div class="footer">';
            html += '<span>' + data.receiptNumber + ' - ' + data.amount + ' paid on ' + data.datePaid + '</span>';
            html += '<span>Page 1 of 1</span>';
            html += '</div></div>';
            html += '<script>window.onload=function(){setTimeout(function(){window.print();},1000);};<\/script>';
            html += '</body></html>';
            return html;
        }
    </script>
</body>
</html>
