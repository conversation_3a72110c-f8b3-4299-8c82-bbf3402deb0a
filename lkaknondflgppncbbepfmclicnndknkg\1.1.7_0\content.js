// Augment Code 自动注册助手 - 内容脚本
(function() {
    'use strict';

    // 全局变量
    let hasExecuted = false;
    let panelManuallyClosedByUser = false;
    const DEBUG = true;
    // 添加新的全局变量，用于跟踪验证码获取状态
    let verificationCodeFetching = false;
    let verificationPageObserver = null;
    // 添加新的全局变量，用于跟踪验证码页面的状态
    let verificationPageHandled = false;

    // 可用的邮箱域名列表
    const AVAILABLE_EMAIL_DOMAINS = [
        "niubea.site",
        "niubea.fun",
        "niubea.online",
        "niubea.xyz"
    ];

    // 随机选择一个邮箱域名
    function getRandomEmailDomain() {
        const randomIndex = Math.floor(Math.random() * AVAILABLE_EMAIL_DOMAINS.length);
        return AVAILABLE_EMAIL_DOMAINS[randomIndex];
    }

    // 配置信息
    const defaultConfig = {
        emailPrefix: "", // 临时邮箱前缀
        emailDomain: "niubea.site", // 注册时使用的邮箱域名
        realEmailDomain: "mail.niubea.site",
        autoFill: true, // 自动填写表单
        emailCodeType: "AUTO", // 验证码获取方式，AUTO或INPUT
        emailPin: "", // 邮箱PIN码
        tempEmailApi: "https://mail.niubea.site", // TempMail API地址
        emailVerificationRetries: 5, // 最大重试次数
        emailVerificationWait: 10, // 每次重试等待时间(秒)
        enableLogging: true
    };

    // 添加基础样式
    const styleSheet = document.createElement('style');
    styleSheet.id = 'augment-code-styles';
    styleSheet.innerHTML = `
        /* 面板基础样式 */
        #cursor-auto-panel {
            background: rgba(25, 25, 35, 0.95);
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 15px;
            transform: translateZ(0);
        }
        
        /* 标题栏样式 */
        #cursor-auto-panel .drag-handle {
            background: linear-gradient(135deg, #4776E6, #8E54E9);
            margin: -15px -15px 10px -15px;
            padding: 10px;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        
        /* 标题文本 */
        #cursor-auto-panel .drag-handle span.title {
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        
        /* 按钮通用样式 */
        #cursor-auto-panel button {
            border: none;
            color: white;
            padding: 8px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            margin-bottom: 5px;
        }
        
        /* 按钮悬停效果 */
        #cursor-auto-panel button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
        }
        
        /* 按钮点击效果 */
        #cursor-auto-panel button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
        }
        
        /* 填写表单按钮 */
        #cursor-auto-panel button#fill-signup {
            background: linear-gradient(135deg, #9D50BB, #6E48AA);
        }
        
        /* 填写密码按钮 */
        #cursor-auto-panel button#fill-password {
            background: linear-gradient(135deg, #4776E6, #8E54E9);
        }
        
        /* 填写验证码按钮 */
        #cursor-auto-panel button#fill-verify {
            background: linear-gradient(135deg, #11998e, #38ef7d);
        }
        
        /* 重新填写按钮 */
        #cursor-auto-panel button#fill-again {
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
        }
        
        /* 生成新账号按钮 */
        #cursor-auto-panel button#generate-new {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }
        
        /* 返回主菜单按钮 */
        #cursor-auto-panel button#back-to-main {
            background: linear-gradient(135deg, #78909C, #546E7A);
        }

        /* 复制按钮 */
        #cursor-auto-panel button[id^="copy"] {
            background: linear-gradient(135deg, #11998e, #38ef7d);
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* 高亮按钮样式 */
        #cursor-auto-panel button.highlighted {
            background: linear-gradient(135deg, #FFD700, #FFA500) !important;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.6) !important;
            animation: pulse 2s infinite !important;
            border: 2px solid #FFD700 !important;
        }

        /* 置灰按钮样式 */
        #cursor-auto-panel button.disabled {
            background: linear-gradient(135deg, #9E9E9E, #757575) !important;
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        /* 高亮按钮脉冲动画 */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
            }
            50% {
                box-shadow: 0 0 25px rgba(255, 215, 0, 0.9);
            }
            100% {
                box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
            }
        }
        
        /* 标题样式 */
        #cursor-auto-panel h3 {
            color: white;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding-bottom: 8px;
            margin-top: 0;
            font-weight: 600;
        }
        
        /* 信息文本样式 */
        #cursor-auto-panel p {
            color: rgba(255,255,255,0.9);
            margin: 5px 0;
        }
        
        /* 显示按钮样式 */
        #cursor-auto-show-button {
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            background: linear-gradient(135deg, #4776E6, #8E54E9) !important;
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 8px !important;
            cursor: pointer !important;
            z-index: 2147483647 !important; /* 最高z-index */
            font-family: system-ui, -apple-system, sans-serif !important;
            /* 删除display:none，让JavaScript控制显示状态 */
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3) !important;
            font-weight: 500 !important;
            width: auto !important;
            max-width: 150px !important;
            min-width: 120px !important;
            font-size: 14px !important;
            padding: 8px 15px !important;
            margin: 0 !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            transition: transform 0.3s ease, box-shadow 0.3s ease !important;
            line-height: 1.5 !important;
            white-space: nowrap !important;
            height: auto !important;
            min-height: auto !important;
            text-align: center !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        #cursor-auto-show-button:hover {
            background: linear-gradient(135deg, #6a1b9a, #9c27b0, #d81b60) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4) !important;
        }
    `;
    document.head.appendChild(styleSheet);

    // 创建和应用showButton样式表
    const showButtonStyleSheet = document.createElement('style');
    showButtonStyleSheet.id = 'cursor-auto-show-button-style';
    showButtonStyleSheet.textContent = `
        #cursor-auto-show-button {
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            background: linear-gradient(135deg, #4776E6, #8E54E9) !important;
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 8px !important;
            cursor: pointer !important;
            z-index: 2147483647 !important; /* 最高z-index确保按钮在最上层 */
            font-family: system-ui, -apple-system, sans-serif !important;
            /* 去掉display:none，由JavaScript控制显示状态 */
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3) !important;
            font-weight: 500 !important;
            width: auto !important;
            max-width: 150px !important;
            min-width: 120px !important;
            font-size: 14px !important;
            padding: 8px 15px !important;
            margin: 0 !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            transition: transform 0.3s ease, box-shadow 0.3s ease !important;
            line-height: 1.5 !important;
            white-space: nowrap !important;
            height: auto !important;
            min-height: auto !important;
            text-align: center !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        #cursor-auto-show-button:hover {
            background: linear-gradient(135deg, #6a1b9a, #9c27b0, #d81b60) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4) !important;
        }
    `;
    document.head.appendChild(showButtonStyleSheet);
    
    // 创建显示按钮的函数
    function createShowButton() {
        // 检查是否已存在按钮
        let existingButton = document.getElementById('cursor-auto-show-button');
        if (existingButton) {
            logDebug('显示按钮已存在，跳过创建');
            return existingButton;
        }

        logDebug('创建新的显示按钮');
        const button = document.createElement('button');
        button.id = 'cursor-auto-show-button';
        button.innerHTML = '<span style="font-weight: bold;">Augment Code</span>';

        // 修改：初始状态设置为显示，而不是隐藏
        button.style.cssText = 'display: block !important;';
        document.body.appendChild(button);

        // 确保按钮的z-index足够高
        button.style.zIndex = '2147483647';

        // 添加点击事件处理
        button.addEventListener('click', () => {
            console.debug('[Augment Code] 点击显示按钮，创建主面板');
            createMainPanel();
            // 确保按钮隐藏，使用!important覆盖CSS规则
            button.style.cssText = 'display: none !important;';
        });

        return button;
    }

    // 创建显示按钮
    const showButton = createShowButton();

    // 调试输出函数
    function logDebug(message) {
        chrome.runtime.sendMessage({ type: "getConfig" }, (response) => {
            if (response && response.config && response.config.enableLogging) {
                // console.log(`[Augment Code助手] ${message}`);
            }
        });
    }

    // PDF生成相关函数
    // 随机生成Invoice号码 (格式: 8位字符-4位数字)
    function generateInvoiceNumber() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        const numbers = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `${result}-${numbers}`;
    }

    // 随机生成收据号码 (格式: 4位数字-4位数字)
    function generateReceiptNumber() {
        const part1 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        const part2 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `${part1}-${part2}`;
    }

    // 随机生成支付方式
    function generatePaymentMethod() {
        const cardTypes = ['Visa', 'MasterCard', 'American Express', 'Discover'];
        const lastFourDigits = [
            '1234', '5678', '9012', '3456', '7890', '2468', '1357', '8024',
            '4567', '8901', '2345', '6789', '0123', '4680', '1379', '5791',
            '2580', '3691', '4702', '5813', '6924', '7035', '8146', '9257'
        ];

        const randomCardType = cardTypes[Math.floor(Math.random() * cardTypes.length)];
        const randomLastFour = lastFourDigits[Math.floor(Math.random() * lastFourDigits.length)];

        return `${randomCardType} - ${randomLastFour}`;
    }

    // 随机生成日期 (当前日期最近15天内)
    function generateRandomDate() {
        const today = new Date();
        const fifteenDaysAgo = new Date(today);
        fifteenDaysAgo.setDate(today.getDate() - 15);

        const timeDiff = today.getTime() - fifteenDaysAgo.getTime();
        const randomTime = Math.random() * timeDiff;
        const randomDate = new Date(fifteenDaysAgo.getTime() + randomTime);

        return randomDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // 随机生成收票人信息
    function generateBillToInfo(email) {
        // 随机英文名称组合
        const firstNames = [
            'JAMES', 'JOHN', 'ROBERT', 'MICHAEL', 'WILLIAM', 'DAVID', 'RICHARD', 'CHARLES', 'JOSEPH', 'THOMAS',
            'CHRISTOPHER', 'DANIEL', 'PAUL', 'MARK', 'DONALD', 'STEVEN', 'KENNETH', 'JOSHUA', 'KEVIN', 'BRIAN',
            'MARY', 'PATRICIA', 'JENNIFER', 'LINDA', 'ELIZABETH', 'BARBARA', 'SUSAN', 'JESSICA', 'SARAH', 'KAREN',
            'NANCY', 'LISA', 'BETTY', 'HELEN', 'SANDRA', 'DONNA', 'CAROL', 'RUTH', 'SHARON', 'MICHELLE'
        ];

        const lastNames = [
            'SMITH', 'JOHNSON', 'WILLIAMS', 'BROWN', 'JONES', 'GARCIA', 'MILLER', 'DAVIS', 'RODRIGUEZ', 'MARTINEZ',
            'HERNANDEZ', 'LOPEZ', 'GONZALEZ', 'WILSON', 'ANDERSON', 'THOMAS', 'TAYLOR', 'MOORE', 'JACKSON', 'MARTIN',
            'LEE', 'PEREZ', 'THOMPSON', 'WHITE', 'HARRIS', 'SANCHEZ', 'CLARK', 'RAMIREZ', 'LEWIS', 'ROBINSON',
            'WALKER', 'YOUNG', 'ALLEN', 'KING', 'WRIGHT', 'SCOTT', 'TORRES', 'NGUYEN', 'HILL', 'FLORES'
        ];

        // 美国真实地址库
        const usAddresses = [
            '2017 North Hartford Drive',
            '1425 West Maple Street',
            '3892 East Oak Avenue',
            '756 South Pine Road',
            '2134 North Elm Street',
            '4567 West Cedar Lane',
            '1289 East Birch Drive',
            '3456 South Willow Way',
            '789 North Spruce Street',
            '2345 West Hickory Avenue',
            '5678 East Magnolia Drive',
            '1567 South Dogwood Lane',
            '3789 North Sycamore Street',
            '4123 West Poplar Avenue',
            '2678 East Chestnut Drive',
            '1890 South Walnut Street',
            '3234 North Cherry Lane',
            '4567 West Peach Avenue',
            '1678 East Apple Drive',
            '2890 South Orange Street',
            '3567 North Lemon Lane',
            '4234 West Lime Avenue',
            '1789 East Grape Drive',
            '2456 South Berry Street',
            '3123 North Plum Lane',
            '4789 West Fig Avenue',
            '1345 East Date Drive',
            '2567 South Olive Street',
            '3890 North Palm Lane',
            '4456 West Rose Avenue'
        ];

        // 美国城市、州、邮编组合
        const usCityStateZip = [
            'Fayetteville, Arkansas 72701',
            'Birmingham, Alabama 35203',
            'Phoenix, Arizona 85001',
            'Little Rock, Arkansas 72201',
            'Los Angeles, California 90210',
            'San Francisco, California 94102',
            'Denver, Colorado 80202',
            'Hartford, Connecticut 06103',
            'Miami, Florida 33101',
            'Atlanta, Georgia 30303',
            'Chicago, Illinois 60601',
            'Indianapolis, Indiana 46204',
            'Des Moines, Iowa 50309',
            'Wichita, Kansas 67202',
            'Louisville, Kentucky 40202',
            'New Orleans, Louisiana 70112',
            'Portland, Maine 04101',
            'Baltimore, Maryland 21201',
            'Boston, Massachusetts 02101',
            'Detroit, Michigan 48226',
            'Minneapolis, Minnesota 55401',
            'Jackson, Mississippi 39201',
            'Kansas City, Missouri 64108',
            'Billings, Montana 59101',
            'Omaha, Nebraska 68102',
            'Las Vegas, Nevada 89101',
            'Manchester, New Hampshire 03101',
            'Newark, New Jersey 07102',
            'Albuquerque, New Mexico 87101',
            'New York, New York 10001',
            'Charlotte, North Carolina 28202',
            'Fargo, North Dakota 58102',
            'Columbus, Ohio 43215',
            'Oklahoma City, Oklahoma 73102',
            'Portland, Oregon 97201',
            'Philadelphia, Pennsylvania 19103',
            'Providence, Rhode Island 02903',
            'Charleston, South Carolina 29401',
            'Sioux Falls, South Dakota 57104',
            'Nashville, Tennessee 37201',
            'Houston, Texas 77002',
            'Salt Lake City, Utah 84101',
            'Burlington, Vermont 05401',
            'Virginia Beach, Virginia 23451',
            'Seattle, Washington 98101',
            'Charleston, West Virginia 25301',
            'Milwaukee, Wisconsin 53202',
            'Cheyenne, Wyoming 82001'
        ];

        const randomFirstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const randomLastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        const randomAddress = usAddresses[Math.floor(Math.random() * usAddresses.length)];
        const randomCityStateZip = usCityStateZip[Math.floor(Math.random() * usCityStateZip.length)];

        return {
            name: `${randomFirstName} ${randomLastName}`,
            address1: randomAddress,
            address2: randomCityStateZip,
            city: '', // 不使用，因为address2已包含完整信息
            state: '', // 不使用，因为address2已包含完整信息
            country: 'United States',
            email: email
        };
    }

    // 生成日期范围 (基于支付日期)
    function generateDateRange(datePaid) {
        const paidDate = new Date(datePaid);
        const startDate = new Date(paidDate);
        const endDate = new Date(paidDate);
        endDate.setMonth(endDate.getMonth() + 1);

        const formatDate = (date) => {
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric'
            });
        };

        return `${formatDate(startDate)} - ${formatDate(endDate)}, ${paidDate.getFullYear()}`;
    }

    // 主要的Invoice生成函数
    function generateRandomInvoice(email) {
        const invoiceNumber = generateInvoiceNumber();
        const receiptNumber = generateReceiptNumber();
        const datePaid = generateRandomDate();
        const paymentMethod = generatePaymentMethod();
        const billTo = generateBillToInfo(email);

        // 固定金额：$20.00
        const amount = '$20.00';

        const description = 'Cursor Pro';
        const dateRange = generateDateRange(datePaid);

        return {
            invoiceNumber,
            receiptNumber,
            datePaid,
            paymentMethod,
            billTo,
            amount,
            description,
            dateRange
        };
    }

    // Windsurf版本的Invoice生成函数
    function generateRandomWindsurfInvoice(email) {
        const invoiceNumber = generateInvoiceNumber();
        const receiptNumber = generateReceiptNumber();
        const datePaid = generateRandomDate();
        const paymentMethod = generatePaymentMethod();
        const billTo = generateBillToInfo(email);

        // 随机选择金额：要么$6.90要么$15.00
        const amounts = ['$6.90', '$15.00'];
        const amount = amounts[Math.floor(Math.random() * amounts.length)];

        const description = 'Windsurf Pro';
        const dateRange = generateDateRange(datePaid);

        return {
            invoiceNumber,
            receiptNumber,
            datePaid,
            paymentMethod,
            billTo,
            amount,
            description,
            dateRange
        };
    }

    // 创建PDF页面HTML
    function createInvoiceHTML(data) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - ${data.invoiceNumber}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 引入与图片中字体相似的Inter字体 */
        .invoice-container {
            /* 设置页面背景色，以凸显A4纸张效果 */
            background-color: #f3f4f6;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .page {
            /* A4纸张尺寸 */
            width: 210mm;
            min-height: 297mm;

            /* 在屏幕上居中显示并添加边距和阴影 */
            margin: 2cm 0;
            padding: 40px 50px;
            box-shadow: 0 0 1cm rgba(0,0,0,0.3);
            background-color: #ffffff;

            /* 关键：使用flexbox使页脚能固定在底部 */
            display: flex;
            flex-direction: column;

            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: #000000;
        }

        .content-wrapper {
            flex-grow: 1;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            line-height: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: flex-end;
            margin-right: 0px;
        }

        .logo img {
            width: auto;
            height: 80px;
        }

        .logo-text {
            font-size: 28px;
            font-weight: 700;
        }

        .invoice-meta-section {
            margin-bottom: 20px;
        }

        .invoice-info table {
            border-collapse: collapse;
        }

        .invoice-info td {
            padding: 1px 0;
            vertical-align: top;
            line-height: 1.6;
            font-size: 13px;
        }

        /* 标签样式 (e.g., "Invoice number") */
        .invoice-info td:first-child {
            color: #000000;
            padding-right: 20px;
            font-weight: 400; /* Regular weight */
        }

        /* 值样式 (e.g., "70C68990-0002") */
        .invoice-info td:last-child {
            font-weight: 500; /* Medium weight, slightly heavier than regular */
        }

        .address-section {
            display: flex;
            justify-content: flex-start;
            gap: 120px;
            margin-bottom: 20px;
            font-size: 13px;
        }

        .address p {
            margin: 0;
            line-height: 1.6;
        }

        .address-title {
            font-weight: 500;
        }

        .summary {
            margin-bottom: 40px;
        }

        .summary h2 {
            font-size: 16px;
            font-weight: 700;
            margin: 0;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .items-table thead th {
            text-align: right;
            border-bottom: 1px solid #9ca3af;
            padding-bottom: 8px;
            font-weight: 400;
            color: #000000;
            font-size: 12px;
        }

        .items-table thead th:first-child {
            text-align: left;
        }

        .items-table tbody td {
            padding: 8px 0;
            border-bottom: none;
            text-align: right;
            vertical-align: top;
        }

        .items-table tbody td:first-child {
            text-align: left;
        }

        .items-table th:nth-child(2),
        .items-table td:nth-child(2) {
            width: 15%;
        }

        .items-table th:nth-child(3),
        .items-table td:nth-child(3) {
            width: 15%;
        }
        .items-table th:nth-child(4),
        .items-table td:nth-child(4) {
            width: 13%;
        }

        .item-description {
            color: #000000;
        }

        .item-date {
            font-size: 12px;
            margin-top: 2px;
        }

        .totals-divider {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 0 0 20px 0;
        }

        .totals-section {
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            width: 350px;
            font-size: 13px;
        }

        .totals-table tr {
            border-top: 1px solid #e5e7eb;
        }

        .totals-table td {
            padding: 1px 0;
        }

        .totals-table td:last-child {
            text-align: right;
            font-weight: 500;
        }

        .totals-table tr td {
            border-top: 1px solid #e5e7eb;
        }

        .totals-table tr:last-child td {
            font-weight: 700;
        }

        .footer {
            margin-top: auto;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }

        /* 打印样式 */
        @media print {
            .invoice-container {
                background-color: #ffffff;
                margin: 0;
                padding: 0;
            }
            .page {
                width: 100%;
                min-height: 0;
                margin: 0;
                box-shadow: none;
                border: none;
                padding: 0;
            }
            .footer {
                margin-top: 380px;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="page">
            <div class="content-wrapper">
                <div class="header">
                    <h1>Receipt</h1>
                    <div class="logo">
                        <img src="https://liubao.org.cn/image/cursor.png" alt="Cursor Logo" />
                    </div>
                </div>

                <div class="invoice-meta-section">
                    <div class="invoice-info">
                        <table>
                            <tbody>
                                <tr>
                                    <td><b>Invoice number</b></td>
                                    <td><b>${data.invoiceNumber}</b></td>
                                </tr>
                                <tr>
                                    <td><b>Receipt number</b></td>
                                    <td><b>${data.receiptNumber}</b></td>
                                </tr>
                                <tr>
                                    <td><b>Date paid</b></td>
                                    <td><b>${data.datePaid}</b></td>
                                </tr>
                                <tr>
                                    <td><b>Payment method</b></td>
                                    <td><b>${data.paymentMethod}</b></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="address-section">
                    <div class="address">
                        <p><b>Cursor AI Inc.</b></p>
                        <p>548 Market St PMB 35410</p>
                        <p>San Francisco, CA 94104-5401</p>
                        <p>United States</p>
                        <p><EMAIL></p>
                        <p>US EIN 88-1234567</p>
                    </div>
                    <div class="address">
                        <p><b>Bill to</b></p>
                        <p>${data.billTo.name}</p>
                        <p>${data.billTo.address1}</p>
                        <p>${data.billTo.address2}</p>
                        <p>${data.billTo.country}</p>
                        <p>${data.billTo.email}</p>
                    </div>
                </div>

                <div class="summary">
                    <h2>${data.amount} paid on ${data.datePaid}</h2>
                </div>

                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th>Qty</th>
                            <th>Unit price</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="item-description">${data.description}</div>
                                <div class="item-date">${data.dateRange}</div>
                            </td>
                            <td>1</td>
                            <td>${data.amount}</td>
                            <td>${data.amount}</td>
                        </tr>
                    </tbody>
                </table>

                <div class="totals-section">
                    <table class="totals-table">
                        <tbody>
                            <tr>
                                <td>Subtotal</td>
                                <td>${data.amount}</td>
                            </tr>
                            <tr>
                                <td>Total</td>
                                <td>${data.amount}</td>
                            </tr>
                            <tr>
                                <td><b>Amount paid</b></td>
                                <td><b>${data.amount}</b></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="footer">
                <span>${data.receiptNumber} - ${data.amount} paid on ${data.datePaid}</span>
                <span>Page 1 of 1</span>
            </div>
        </div>
    </div>
    <script>
        // 页面加载完成后自动打印
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>`;
    }

    // 创建Windsurf PDF页面HTML
    function createWindsurfInvoiceHTML(data) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - ${data.invoiceNumber}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 引入与图片中字体相似的Inter字体 */
        .invoice-container {
            /* 设置页面背景色，以凸显A4纸张效果 */
            background-color: #f3f4f6;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .page {
            /* A4纸张尺寸 */
            width: 210mm;
            min-height: 297mm;

            /* 在屏幕上居中显示并添加边距和阴影 */
            margin: 2cm 0;
            padding: 40px 50px;
            box-shadow: 0 0 1cm rgba(0,0,0,0.3);
            background-color: #ffffff;

            /* 关键：使用flexbox使页脚能固定在底部 */
            display: flex;
            flex-direction: column;

            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: #000000;
        }

        .content-wrapper {
            flex-grow: 1;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            line-height: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: flex-end;
            margin-right: -25px;
        }

        .logo img {
            width: auto;
            height: 80px;
        }

        .logo-text {
            font-size: 28px;
            font-weight: 700;
        }

        .invoice-meta-section {
            margin-bottom: 20px;
        }

        .invoice-info table {
            border-collapse: collapse;
        }

        .invoice-info td {
            padding: 1px 0;
            vertical-align: top;
            line-height: 1.6;
            font-size: 13px;
        }

        /* 标签样式 (e.g., "Invoice number") */
        .invoice-info td:first-child {
            color: #000000;
            padding-right: 20px;
            font-weight: 400; /* Regular weight */
        }

        /* 值样式 (e.g., "70C68990-0002") */
        .invoice-info td:last-child {
            font-weight: 500; /* Medium weight, slightly heavier than regular */
        }

        .address-section {
            display: flex;
            justify-content: flex-start;
            gap: 120px;
            margin-bottom: 20px;
            font-size: 13px;
        }

        .address p {
            margin: 0;
            line-height: 1.6;
        }

        .address-title {
            font-weight: 500;
        }

        .summary {
            margin-bottom: 40px;
        }

        .summary h2 {
            font-size: 16px;
            font-weight: 700;
            margin: 0;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .items-table thead th {
            text-align: right;
            border-bottom: 1px solid #9ca3af;
            padding-bottom: 8px;
            font-weight: 400;
            color: #000000;
            font-size: 12px;
        }

        .items-table thead th:first-child {
            text-align: left;
        }

        .items-table tbody td {
            padding: 8px 0;
            border-bottom: none;
            text-align: right;
            vertical-align: top;
        }

        .items-table tbody td:first-child {
            text-align: left;
        }

        .items-table th:nth-child(2),
        .items-table td:nth-child(2) {
            width: 15%;
        }

        .items-table th:nth-child(3),
        .items-table td:nth-child(3) {
            width: 15%;
        }
        .items-table th:nth-child(4),
        .items-table td:nth-child(4) {
            width: 13%;
        }

        .item-description {
            color: #000000;
        }

        .item-date {
            font-size: 12px;
            margin-top: 2px;
        }

        .totals-divider {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 0 0 20px 0;
        }

        .totals-section {
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            width: 350px;
            font-size: 13px;
        }

        .totals-table tr {
            border-top: 1px solid #e5e7eb;
        }

        .totals-table td {
            padding: 1px 0;
        }

        .totals-table td:last-child {
            text-align: right;
            font-weight: 500;
        }

        .totals-table tr td {
            border-top: 1px solid #e5e7eb;
        }

        .totals-table tr:last-child td {
            font-weight: 700;
        }

        .footer {
            margin-top: auto;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }

        /* 打印样式 */
        @media print {
            .invoice-container {
                background-color: #ffffff;
                margin: 0;
                padding: 0;
            }
            .page {
                width: 100%;
                min-height: 0;
                margin: 0;
                box-shadow: none;
                border: none;
                padding: 0;
            }
            .footer {
                margin-top: 380px;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="page">
            <div class="content-wrapper">
                <div class="header">
                    <h1>Receipt</h1>
                    <div class="logo">
                        <img src="https://liubao.org.cn/image/windsurf1.png" alt="Windsurf Logo" />
                    </div>
                </div>

                <div class="invoice-meta-section">
                    <div class="invoice-info">
                        <table>
                            <tbody>
                                <tr>
                                    <td><b>Invoice number</b></td>
                                    <td><b>${data.invoiceNumber}</b></td>
                                </tr>
                                <tr>
                                    <td><b>Receipt number</b></td>
                                    <td><b>${data.receiptNumber}</b></td>
                                </tr>
                                <tr>
                                    <td><b>Date paid</b></td>
                                    <td><b>${data.datePaid}</b></td>
                                </tr>
                                <tr>
                                    <td><b>Payment method</b></td>
                                    <td><b>${data.paymentMethod}</b></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="address-section">
                    <div class="address">
                        <p><b>Windsurf</b></p>
                        <p>900 Villa Street</p>
                        <p>Mountain View, California 94041</p>
                        <p>United States</p>
                        <p><EMAIL></p>
                        <p>US EIN 87-1068811</p>
                    </div>
                    <div class="address">
                        <p><b>Bill to</b></p>
                        <p>${data.billTo.name}</p>
                        <p>${data.billTo.address1}</p>
                        <p>${data.billTo.address2}</p>
                        <p>${data.billTo.country}</p>
                        <p>${data.billTo.email}</p>
                    </div>
                </div>

                <div class="summary">
                    <h2>${data.amount} paid on ${data.datePaid}</h2>
                </div>

                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th>Qty</th>
                            <th>Unit price</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="item-description">${data.description}</div>
                                <div class="item-date">${data.dateRange}</div>
                            </td>
                            <td>1</td>
                            <td>${data.amount}</td>
                            <td>${data.amount}</td>
                        </tr>
                    </tbody>
                </table>

                <div class="totals-section">
                    <table class="totals-table">
                        <tbody>
                            <tr>
                                <td>Subtotal</td>
                                <td>${data.amount}</td>
                            </tr>
                            <tr>
                                <td>Total</td>
                                <td>${data.amount}</td>
                            </tr>
                            <tr>
                                <td><b>Amount paid</b></td>
                                <td><b>${data.amount}</b></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="footer">
                <span>${data.receiptNumber} - ${data.amount} paid on ${data.datePaid}</span>
                <span>Page 1 of 1</span>
            </div>
        </div>
    </div>
    <script>
        // 页面加载完成后自动打印
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>`;
    }

    // 生成并打开PDF
    function generateAndOpenPDF(email) {
        try {
            logDebug(`开始生成Cursor付费凭证PDF，邮箱: ${email}`);

            // 生成随机Invoice数据
            const invoiceData = generateRandomInvoice(email);

            // 创建HTML内容
            const htmlContent = createInvoiceHTML(invoiceData);

            // 创建Blob对象
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            // 在新窗口中打开PDF
            const newWindow = window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            if (newWindow) {
                logDebug('PDF页面已在新窗口中打开');
                showNotification('Cursor付费凭证已生成，请在新窗口中打印保存');

                // 清理URL对象
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 10000);
            } else {
                throw new Error('无法打开新窗口，请检查浏览器弹窗设置');
            }

        } catch (error) {
            logDebug(`生成PDF失败: ${error.message}`);
            showNotification(`生成Cursor付费凭证失败: ${error.message}`, 'error');
        }
    }

    // 生成并打开Windsurf PDF
    function generateAndOpenWindsurfPDF(email) {
        try {
            logDebug(`开始生成Windsurf付费凭证PDF，邮箱: ${email}`);

            // 生成随机Invoice数据（Windsurf版本）
            const invoiceData = generateRandomWindsurfInvoice(email);

            // 创建HTML内容（Windsurf版本）
            const htmlContent = createWindsurfInvoiceHTML(invoiceData);

            // 创建Blob对象
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            // 在新窗口中打开PDF
            const newWindow = window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            if (newWindow) {
                logDebug('Windsurf PDF页面已在新窗口中打开');
                showNotification('Windsurf付费凭证已生成，请在新窗口中打印保存');

                // 清理URL对象
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 10000);
            } else {
                throw new Error('无法打开新窗口，请检查浏览器弹窗设置');
            }

        } catch (error) {
            logDebug(`生成Windsurf PDF失败: ${error.message}`);
            showNotification(`生成Windsurf付费凭证失败: ${error.message}`, 'error');
        }
    }

    // 获取用户注册邮箱
    async function getUserEmail() {
        try {
            // 首先尝试从页面提取邮箱
            const emailFromPage = extractEmailFromPage();
            if (emailFromPage) {
                logDebug(`从页面提取到邮箱: ${emailFromPage}`);
                return emailFromPage;
            }

            // 从存储的账号信息中获取邮箱
            const response = await new Promise(resolve => {
                chrome.runtime.sendMessage({ type: "getAccountInfo" }, (response) => {
                    resolve(response);
                });
            });

            const accountInfo = response?.accountInfo || {};
            if (accountInfo.email) {
                logDebug(`从存储账号信息中获取邮箱: ${accountInfo.email}`);
                return accountInfo.email;
            }

            // 从配置中获取邮箱
            const config = await getConfig();
            if (config?.emailPrefix && config?.emailDomain) {
                const configEmail = `${config.emailPrefix}@${config.emailDomain}`;
                logDebug(`从配置中构建邮箱: ${configEmail}`);
                return configEmail;
            }

            // 如果都没有，返回默认邮箱
            const defaultEmail = '<EMAIL>';
            logDebug(`使用默认邮箱: ${defaultEmail}`);
            return defaultEmail;

        } catch (error) {
            logDebug(`获取用户邮箱失败: ${error.message}`);
            return '<EMAIL>';
        }
    }

    // 添加联系作者按钮的通用函数
    function addContactAuthorButton(containerId = 'augment-code-panel') {
        const container = document.getElementById(containerId);
        if (!container) return;

        // 检查是否已经存在联系作者按钮
        if (document.getElementById('contact-author-universal')) return;

        // 创建联系作者按钮容器
        const contactContainer = document.createElement('div');
        contactContainer.innerHTML = `
            <div style="margin-top:10px;border-top:1px solid rgba(255,255,255,0.2);padding-top:10px;">
                <button id="contact-author-universal" style="width:100%;padding:12px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">📱 联系作者</button>
            </div>
            <div id="wechat-qr-container-universal" style="display:none;margin-top:15px;text-align:center;">
                <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
            </div>
        `;

        // 添加到容器末尾
        container.appendChild(contactContainer);

        // 绑定联系作者按钮事件
        document.getElementById('contact-author-universal')?.addEventListener('click', () => {
            logDebug('点击通用联系作者按钮');
            const qrContainer = document.getElementById('wechat-qr-container-universal');
            const contactBtn = document.getElementById('contact-author-universal');

            if (qrContainer && contactBtn) {
                if (qrContainer.style.display === 'none') {
                    // 展开二维码
                    qrContainer.style.display = 'block';
                    contactBtn.textContent = '📱 收起联系';
                    contactBtn.style.background = 'linear-gradient(135deg, #FF6B6B, #FF5252) !important';
                } else {
                    // 收起二维码
                    qrContainer.style.display = 'none';
                    contactBtn.textContent = '📱 联系作者';
                    contactBtn.style.background = 'linear-gradient(135deg, #07C160, #00A854) !important';
                }
            }
        });
    }

    // 错误日志打印函数
    function errorDebug(message) {
        chrome.runtime.sendMessage({ type: "getConfig" }, (response) => {
            if (response && response.config && response.config.enableLogging) {
                // console.error(`[Augment Code助手] ${message}`);
            }
        });
    }

    // 生成随机字符串（用于邮箱前缀，以"aug"开头）
    function generateRandomString(length = 8) {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        // 减少2位长度，因为要加上"aug"前缀
        const randomLength = Math.max(1, length - 2);
        for (let i = 0; i < randomLength; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        // 返回以"aug"开头的字符串
        return 'aug' + result;
    }

    // 生成随机名字
    function generateRandomName(length = 6) {
        // 只使用字母字符，不包含数字
        const chars = 'abcdefghijklmnopqrstuvwxyz';
        let name = chars.charAt(Math.floor(Math.random() * chars.length)).toUpperCase();

        for (let i = 1; i < length; i++) {
            name += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        return name;
    }

    // 生成随机密码
    function generateRandomPassword(length = 12) {
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        const specials = '!@#$%^&*()_+';
        const numbers = '0123456789';
        let password = '';

        // 确保密码包含必要的字符类型
        password += letters.charAt(Math.floor(Math.random() * 26)); // 大写字母
        password += letters.charAt(Math.floor(Math.random() * 26) + 26); // 小写字母
        password += specials.charAt(Math.floor(Math.random() * specials.length)); // 特殊字符

        // 添加至少两个数字，确保密码中一定有数字
        password += numbers.charAt(Math.floor(Math.random() * numbers.length));
        password += numbers.charAt(Math.floor(Math.random() * numbers.length));

        // 生成剩余的随机字符
        const remainingLength = length - 5;
        for (let i = 0; i < remainingLength; i++) {
            // 增加数字出现的概率到40%
            const rand = Math.random();
            if (rand < 0.4) {
                // 40%的概率添加数字
                password += numbers.charAt(Math.floor(Math.random() * numbers.length));
            } else if (rand < 0.8) {
                // 40%的概率添加字母
                password += letters.charAt(Math.floor(Math.random() * letters.length));
            } else {
                // 20%的概率添加特殊字符
                password += specials.charAt(Math.floor(Math.random() * specials.length));
            }
        }

        // 打乱密码字符顺序
        password = password.split('').sort(() => 0.5 - Math.random()).join('');
        
        // 验证密码中是否包含数字，如果没有则添加数字
        if (!/\d/.test(password)) {
            // 如果没有数字，则随机替换一个字符为数字
            const randomPos = Math.floor(Math.random() * password.length);
            const randomDigit = numbers.charAt(Math.floor(Math.random() * numbers.length));
            password = password.substring(0, randomPos) + randomDigit + password.substring(randomPos + 1);
            logDebug('密码中没有数字，已添加随机数字');
        }
        
        return password;
    }

    // 获取基础邮箱（用于TempMail API）
    async function getBaseEmail() {
        // 尝试从存储中获取之前创建的邮箱
        return new Promise((resolve) => {
            chrome.storage.local.get('createdEmail', (result) => {
                if (result.createdEmail) {
                    logDebug(`使用已创建的邮箱: ${result.createdEmail}`);
                    resolve(result.createdEmail);
                } else {
                    // 如果没有之前创建的邮箱，则使用配置中的默认邮箱
                    chrome.storage.sync.get('augmentCodeConfig', (configResult) => {
                        const config = configResult.augmentCodeConfig || defaultConfig;
                        const email = `${config.emailPrefix || ''}${config.emailPrefix ? '@' : ''}${config.emailDomain}`;
                        logDebug(`没有找到创建的邮箱，使用默认配置: ${email}`);
                        resolve(email);
                    });
                }
            });
        });
    }

    // 获取账号信息
    function getAccountInfo() {
        return new Promise((resolve) => {
            // 首先检查是否已有账号信息
            chrome.runtime.sendMessage({ type: "getConfig" }, (configResponse) => {
                // 更新默认配置，确保使用最新的用户设置
                if (configResponse && configResponse.config) {
                    // 只更新存在的配置项
                    Object.assign(defaultConfig, configResponse.config);
                }

                // 然后获取账号信息
                chrome.runtime.sendMessage({ type: "getAccountInfo" }, async (response) => {
                    if (response && response.accountInfo) {
                        logDebug(`使用已有账号信息: ${JSON.stringify(response.accountInfo)}`);
                        resolve(response.accountInfo);
                    } else {
                        // 创建新账号并更新UI
                        updateInfoPanel(`
                            <h3>正在创建新账号</h3>
                            <p>正在生成随机信息...</p>
                            <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                            <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
                        `);

                        // 生成随机信息
                        const firstName = generateRandomName();
                        const lastName = generateRandomName();
                        const password = generateRandomPassword();

                        // 生成随机邮箱用户名
                        const randomEmailName = generateRandomString(10);

                        // 更新UI显示创建进度
                        updateInfoPanel(`
                            <h3>正在创建新账号</h3>
                            <p>正在创建临时邮箱...</p>
                            <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                            <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
                            <p><small>名: ${firstName}<br>姓: ${lastName}</small></p>
                        `);

                        try {
                            // 调用后台API创建真实邮箱
                            logDebug(`请求创建临时邮箱: ${randomEmailName}`);
                            const emailResponse = await new Promise((resolve, reject) => {
                                chrome.runtime.sendMessage({
                                    type: "createEmail",
                                    apiUrl: defaultConfig.tempEmailApi,
                                    emailName: randomEmailName
                                }, (response) => {
                                    if (response && response.success) {
                                        resolve(response);
                                    } else {
                                        reject(new Error(response?.error || "创建邮箱失败"));
                                    }
                                });
                            });

                            logDebug(`邮箱创建成功: ${JSON.stringify(emailResponse)}`);

                            // 使用API返回的邮箱地址和密码
                            const email = emailResponse.email;
                            const emailPassword = emailResponse.password || password; // 如果API未返回密码则使用生成的随机密码

                            const accountInfo = {
                                firstName: firstName,
                                lastName: lastName,
                                email: email,
                                password: emailPassword
                            };

                            // 更新UI显示创建完成
                            updateInfoPanel(`
                                <h3>账号创建成功</h3>
                                <div style="margin-bottom:10px;">
                                    <p>邮箱: ${email}</p>
                                    <p>密码: ${emailPassword}</p>
                                </div>
                                <div style="display:flex;justify-content:space-between;margin-bottom:10px;">
                                    <button id="copy-new-email" style="background:#4CAF50;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:12px;width:48%;">复制邮箱</button>
                                    <button id="copy-new-password" style="background:#4CAF50;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:12px;width:48%;">复制密码</button>
                                </div>
                                <button id="back-to-main-new" style="background:#607D8B;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;width:100%;">返回主菜单</button>
                            `);



                            // 添加复制按钮功能
                            document.getElementById('copy-new-email')?.addEventListener('click', () => {
                                navigator.clipboard.writeText(email).then(() => {
                                    showNotification('邮箱已复制');
                                }).catch(err => {
                                    showNotification('复制失败: ' + err.message, 'error');
                                });
                            });

                            document.getElementById('copy-new-password')?.addEventListener('click', () => {
                                navigator.clipboard.writeText(emailPassword).then(() => {
                                    showNotification('密码已复制');
                                }).catch(err => {
                                    showNotification('复制失败: ' + err.message, 'error');
                                });
                            });

                            document.getElementById('back-to-main-new')?.addEventListener('click', () => {
                                createMainPanel();
                                showNotification('已返回主菜单');
                            });

                            // 存储账号信息以便后续使用
                            chrome.runtime.sendMessage({ 
                                type: "saveAccountInfo", 
                                email: accountInfo.email,
                                password: accountInfo.password 
                            }, () => {
                                logDebug(`生成新账号信息并存储: ${JSON.stringify(accountInfo)}`);
                                resolve(accountInfo);
                            });
                        } catch (error) {
                            // 创建邮箱失败时的处理
                            logDebug(`邮箱创建失败: ${error.message}`);
                            showNotification(`邮箱创建失败: ${error.message}`, 'error');

                            // 使用本地生成的邮箱作为备选，使用随机域名
                            const fallbackEmail = `${randomEmailName}@${getRandomEmailDomain()}`;

                            const accountInfo = {
                                firstName: firstName,
                                lastName: lastName,
                                email: fallbackEmail,
                                password: password
                            };

                            // 更新UI显示创建失败，但提供备选方案
                            updateInfoPanel(`
                                <h3>邮箱创建出错</h3>
                                <p>出错信息: ${error.message}</p>
                                <div style="margin-bottom:10px;">
                                <p>已创建本地备选方案:</p>
                                    <p>邮箱: ${fallbackEmail}</p>
                                    <p>密码: ${password}</p>
                                </div>
                                <div style="display:flex;justify-content:space-between;margin-bottom:10px;">
                                    <button id="copy-fallback-email" style="background:#4CAF50;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:12px;width:48%;">复制邮箱</button>
                                    <button id="copy-fallback-password" style="background:#4CAF50;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:12px;width:48%;">复制密码</button>
                                </div>
                                <button id="retry-email" style="background:#FF9800;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;width:100%;margin-bottom:5px;">重试创建邮箱</button>
                                <button id="back-to-main-fallback" style="background:#607D8B;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;width:100%;">返回主菜单</button>
                            `);



                            // 添加复制按钮功能
                            document.getElementById('copy-fallback-email')?.addEventListener('click', () => {
                                navigator.clipboard.writeText(fallbackEmail).then(() => {
                                    showNotification('备选邮箱已复制');
                                }).catch(err => {
                                    showNotification('复制失败: ' + err.message, 'error');
                                });
                            });

                            document.getElementById('copy-fallback-password')?.addEventListener('click', () => {
                                navigator.clipboard.writeText(password).then(() => {
                                    showNotification('备选密码已复制');
                                }).catch(err => {
                                    showNotification('复制失败: ' + err.message, 'error');
                                });
                            });

                            document.getElementById('back-to-main-fallback')?.addEventListener('click', () => {
                                createMainPanel();
                                showNotification('已返回主菜单');
                            });

                            document.getElementById('retry-email')?.addEventListener('click', () => {
                                // 清除现有账号并重新创建
                                chrome.runtime.sendMessage({ type: "clearData" }, () => {
                                    getAccountInfo().then(accountInfo => {
                                        showNotification('重新尝试创建账号');
                                    });
                                });
                            });

                            // 存储备选账号信息
                            chrome.runtime.sendMessage({ 
                                type: "saveAccountInfo", 
                                email: accountInfo.email,
                                password: accountInfo.password 
                            }, () => {
                                logDebug(`生成备选账号信息并存储: ${JSON.stringify(accountInfo)}`);
                                resolve(accountInfo);
                            });
                        }
                    }
                });
            });
        });
    }

    // 添加信息悬浮窗
    function addInfoPanel() {
        // 创建面板
        const panel = document.createElement('div');
        panel.id = 'cursor-auto-panel';

        // 使用基础面板样式，默认位置在左侧
        panel.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            left: 20px !important;
            right: auto !important;
            bottom: auto !important;
            z-index: 999999 !important;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-size: 14px !important;
            width: auto !important;
            max-width: 320px !important;
            color: white !important;
            transform: none !important;
            margin: 0 !important;
            line-height: 1.5 !important;
            background: rgba(0, 0, 0, 0.9) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 12px !important;
            padding: 20px !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
        `;

        // 添加拖动句柄
        panel.innerHTML = `
            <div class="drag-handle" style="
                cursor: move !important;
                user-select: none !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                background: linear-gradient(135deg, #4776E6, #8E54E9) !important;
                padding: 12px 15px !important;
                margin: -20px -20px 15px -20px !important;
                border-radius: 12px 12px 0 0 !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            ">
                <div style="
                    display: flex !important;
                    align-items: center !important;
                    font-size: 14px !important;
                    color: white !important;
                    font-weight: 500 !important;
                ">
                    <span style="margin-right: 8px !important; font-size: 16px !important;">≡</span>
                    <span class="title">Augment Code 助手</span>
                </div>
                <div id="cursor-auto-close-button" style="
                    width: 22px !important;
                    height: 22px !important;
                    border-radius: 50% !important;
                    background: rgba(255,255,255,0.2) !important;
                    display: flex !important;
                    justify-content: center !important;
                    align-items: center !important;
                    cursor: pointer !important;
                    color: white !important;
                    font-size: 16px !important;
                    font-weight: bold !important;
                    transition: all 0.2s ease !important;
                ">&times;</div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(panel);

        // 使面板可拖动
        makeDraggable(panel);

        // 添加关闭按钮功能
        const closeButton = document.getElementById('cursor-auto-close-button');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                panel.style.display = 'none';
                // 确保按钮显示，使用!important覆盖CSS规则
                showButton.style.cssText = 'display: block !important;';
                console.debug('[Augment Code] 面板已关闭，显示按钮');
                panelManuallyClosedByUser = true; // 设置面板手动关闭标志
            });

            // 添加鼠标悬停效果
            closeButton.addEventListener('mouseover', () => {
                closeButton.style.background = 'rgba(255,255,255,0.4) !important';
                closeButton.style.transform = 'scale(1.1) !important';
            });
            closeButton.addEventListener('mouseout', () => {
                closeButton.style.background = 'rgba(255,255,255,0.2) !important';
                closeButton.style.transform = 'scale(1) !important';
            });
        }

        // 添加拖动句柄的悬停效果
        const dragHandle = panel.querySelector('.drag-handle');
        if (dragHandle) {
            // 拖动句柄的鼠标悬停效果
            dragHandle.addEventListener('mouseover', () => {
                dragHandle.style.background = 'linear-gradient(135deg, #6a1b9a, #9c27b0, #d81b60) !important';
            });
            dragHandle.addEventListener('mouseout', () => {
                dragHandle.style.background = 'linear-gradient(135deg, #4776E6, #8E54E9) !important';
            });
        }

        return panel;
    }

    // 使元素可拖动
    function makeDraggable(element) {
        let isDragging = false;
        let offset = { x: 0, y: 0 };
        let threshold = 3; // 防止意外点击触发拖动的阈值
        let hasMovedBeyondThreshold = false;
        let initialPosition = { x: 0, y: 0 };

        function onMouseDown(e) {
            // 如果点击的是关闭按钮或者其他可交互元素，不处理拖动
            if (e.target.id === 'cursor-auto-close-button' ||
                e.target.tagName.toLowerCase() === 'button' ||
                e.target.tagName.toLowerCase() === 'input' ||
                e.target.tagName.toLowerCase() === 'a' ||
                e.target.tagName.toLowerCase() === 'textarea') {
                return;
            }

            // 如果有拖动句柄，则只允许通过拖动句柄进行拖动
            const dragHandle = element.querySelector('.drag-handle');
            if (dragHandle) {
                // 检查点击事件的目标是否为拖动句柄或其子元素
                if (!dragHandle.contains(e.target) && !e.target.classList.contains('drag-handle')) {
                    return;
                }
            } else {
                // 没有拖动句柄时，只允许从面板的上部1/3区域拖动
                const rect = element.getBoundingClientRect();
                const upperThirdHeight = rect.height / 3;

                // 如果点击位置不在上部1/3区域，不允许拖动
                if (e.clientY - rect.top > upperThirdHeight) {
                    return;
                }
            }

            isDragging = true;
            hasMovedBeyondThreshold = false;
            initialPosition = { x: e.clientX, y: e.clientY };

            const rect = element.getBoundingClientRect();
            offset = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };

            // 添加拖动开始视觉效果
            element.style.transition = 'none';
            element.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.5), 0 0 25px rgba(255, 215, 0, 0.3)';
            element.style.opacity = '0.9';
            element.style.transform = 'scale(1.02)';

            // 防止文本选择
            document.body.style.userSelect = 'none';
            document.body.style.webkitUserSelect = 'none';
            document.body.style.cursor = 'grabbing';

            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);

            e.preventDefault();
        }

        function onMouseMove(e) {
            if (!isDragging) return;

            // 计算移动距离，检查是否超过阈值
            const dx = e.clientX - initialPosition.x;
            const dy = e.clientY - initialPosition.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (!hasMovedBeyondThreshold && distance < threshold) {
                return;
            }

            hasMovedBeyondThreshold = true;

            let x = e.clientX - offset.x;
            let y = e.clientY - offset.y;

            // 确保面板不会移出屏幕
            const rect = element.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;

            // 增强边界限制，确保面板至少有50%在可视区域内
            const minVisibleWidth = rect.width * 0.5;
            const minVisibleHeight = rect.height * 0.5;

            // 限制左右移动
            x = Math.max(-rect.width + minVisibleWidth, Math.min(x, windowWidth - minVisibleWidth));

            // 限制上下移动，确保标题栏始终可见
            y = Math.max(0, Math.min(y, windowHeight - 40)); // 保证至少标题栏可见

            element.style.left = x + 'px';
            element.style.top = y + 'px';

            // 添加拖动过程中的视觉效果
            const dragHandle = element.querySelector('.drag-handle');
            if (dragHandle) {
                dragHandle.style.background = 'linear-gradient(135deg, #9c27b0, #d81b60, #f44336) !important';
            }

            e.preventDefault();
        }

        function onMouseUp() {
            if (!isDragging) return;
            isDragging = false;

            // 添加拖动结束的视觉效果
            element.style.transition = 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)';
            element.style.boxShadow = '';
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';

            // 恢复文本选择
            document.body.style.userSelect = '';
            document.body.style.webkitUserSelect = '';
            document.body.style.cursor = '';

            // 恢复拖动句柄样式
            const dragHandle = element.querySelector('.drag-handle');
            if (dragHandle) {
                dragHandle.style.background = 'linear-gradient(135deg, #4776E6, #8E54E9) !important';
            }

            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);

            // 如果用户只是点击而没有拖动，不触发任何操作
            if (!hasMovedBeyondThreshold) {
                return;
            }

            // 向background.js发送位置更新消息
            chrome.runtime.sendMessage({
                type: "saveWindowPosition",
                bounds: {
                    left: parseInt(element.style.left),
                    top: parseInt(element.style.top),
                    width: element.offsetWidth,
                    height: element.offsetHeight
                }
            });
        }

        // 添加事件监听器
        element.addEventListener('mousedown', onMouseDown);

        // 返回清理函数，用于移除事件监听器
        return function cleanup() {
            element.removeEventListener('mousedown', onMouseDown);
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        };
    }

    // 更新信息面板内容
    function updateInfoPanel(content) {
        // 获取或创建面板
        let panel = document.getElementById('cursor-auto-panel');
        if (!panel) {
            panel = addInfoPanel();
        }

        // 设置内容
        const contentContainer = panel.querySelector('.panel-content');
        if (contentContainer) {
            contentContainer.innerHTML = content;
        } else {
            // 创建内容容器
            const newContentContainer = document.createElement('div');
            newContentContainer.className = 'panel-content';
            newContentContainer.innerHTML = content;
            panel.appendChild(newContentContainer);
        }

        // 显示面板
        panel.style.display = 'block';
        // 隐藏显示按钮，使用!important确保样式应用
        showButton.style.cssText = 'display: none !important;';

        // 应用动画效果
        setTimeout(() => {
            // 添加按钮动画效果
            applyButtonEffects(panel);
        }, 100);

        return panel;
    }

    // 移除信息面板
    function removeInfoPanel() {
        const panel = document.getElementById('cursor-auto-panel');
        if (panel) {
            panel.remove();
        }
    }

    // 检查登录状态并重新初始化面板
    function checkLoginStatusAndReset() {
        console.debug("[Augment Code] 检查登录状态并重新初始化面板");
        
        // 移除现有面板
        removeInfoPanel();
        
        // 延迟创建新面板，确保DOM已更新
        setTimeout(() => {
            console.debug("[Augment Code] 重新创建面板");
            // 重置标志，以便重新初始化
            hasExecuted = false;
            verificationPageHandled = false;
            
            // 重新初始化
            initializeExtension();
        }, 1000);
    }

    // 应用按钮动画和特效
    function applyButtonEffects(panel) {
        // 获取所有按钮
        const buttons = panel.querySelectorAll('button');

        // 对每个按钮应用动画
        buttons.forEach((btn, index) => {
            // 设置按钮初始透明度为0
            btn.style.opacity = '0';
            btn.style.transform = 'translateY(10px)';
            btn.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

            // 错开按钮出现的时间
            setTimeout(() => {
                btn.style.opacity = '1';
                btn.style.transform = 'translateY(0)';
            }, 100 + (index * 50));

            // 添加按钮点击波纹效果
            btn.addEventListener('click', function (e) {
                // 创建波纹元素
                const ripple = document.createElement('span');
                ripple.classList.add('ripple-effect');

                // 设置波纹样式
                const d = Math.max(this.clientWidth, this.clientHeight);
                ripple.style.width = ripple.style.height = d + 'px';

                // 计算点击位置
                const rect = this.getBoundingClientRect();
                ripple.style.left = (e.clientX - rect.left - (d / 2)) + 'px';
                ripple.style.top = (e.clientY - rect.top - (d / 2)) + 'px';

                // 设置波纹动画样式
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                this.style.overflow = 'hidden';

                // 添加波纹动画样式
                const styleElement = document.getElementById('ripple-style');
                if (!styleElement) {
                    const style = document.createElement('style');
                    style.id = 'ripple-style';
                    style.textContent = `
                        @keyframes ripple {
                            to {
                                transform: scale(2.5);
                                opacity: 0;
                            }
                        }
                    `;
                    document.head.appendChild(style);
                }

                // 添加波纹到按钮
                this.appendChild(ripple);

                // 移除波纹
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    // 获取配置
    function getConfig() {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage({ type: "getConfig" }, (response) => {
                if (response && response.config) {
                    resolve(response.config);
                }
            });
        });
    }

    // 生成PKCE认证对
    function generatePKCEPair() {
        try {
            // 生成随机的code_verifier (43-128个字符)
            const generateRandomString = (length) => {
                const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
                let result = '';
                const randomValues = new Uint8Array(length);
                window.crypto.getRandomValues(randomValues);
                randomValues.forEach(val => result += charset[val % charset.length]);
                return result;
            };

            // 生成code_verifier
            const codeVerifier = generateRandomString(43);

            // 计算code_challenge (SHA-256哈希后Base64编码)
            return new Promise((resolve, reject) => {
                const encoder = new TextEncoder();
                const data = encoder.encode(codeVerifier);

                window.crypto.subtle.digest('SHA-256', data)
                    .then(hashBuffer => {
                        const hashArray = Array.from(new Uint8Array(hashBuffer));
                        const hashBase64 = btoa(String.fromCharCode.apply(null, hashArray))
                            .replace(/\+/g, '-')
                            .replace(/\//g, '_')
                            .replace(/=+$/, '');

                        resolve({
                            codeVerifier: codeVerifier,
                            codeChallenge: hashBase64
                        });
                    })
                    .catch(error => {
                        logDebug(`计算code_challenge失败: ${error.message}`);
                        reject(error);
                    });
            });
        } catch (error) {
            logDebug(`生成PKCE对失败: ${error.message}`);
            return Promise.reject(error);
        }
    }

    // 验证动态口令（基于用户输入）
    async function verifyDynamicCode(userInputCode) {
        try {
            logDebug(`开始验证动态口令，用户输入: "${userInputCode}"`);

            // 通过background.js验证动态口令，避免跨域问题
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    type: "verifyAugklCode",
                    userInputCode: userInputCode
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            logDebug(`动态口令验证响应:`, response);
            return response;
        } catch (error) {
            logDebug(`验证动态口令出错: ${error.message}`);
            return { success: false, message: `验证出错: ${error.message}` };
        }
    }

    // 验证付费口令（基于用户输入）
    async function verifyPaymentCode(userInputCode) {
        try {
            logDebug(`开始验证付费口令，用户输入: "${userInputCode}"`);

            // 通过background.js验证付费口令，避免跨域问题
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    type: "verifyPaymentCode",
                    userInputCode: userInputCode
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            logDebug(`付费口令验证响应:`, response);
            return response;
        } catch (error) {
            logDebug(`验证付费口令出错: ${error.message}`);
            return { success: false, message: `验证出错: ${error.message}` };
        }
    }

    // 创建带密码可见性切换的输入框
    function createPasswordInputWithToggle(inputId, placeholder, inputStyle) {
        return `
            <div style="position: relative; width: 100%; display: block;">
                <input type="password" id="${inputId}" placeholder="${placeholder}" style="${inputStyle} padding-right: 40px;" />
                <button type="button" id="${inputId}-toggle" style="
                    position: absolute;
                    right: 8px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(255,255,255,0.1);
                    border: 1px solid rgba(255,255,255,0.2);
                    color: #fff;
                    cursor: pointer;
                    font-size: 14px;
                    padding: 0;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 28px;
                    height: 28px;
                    border-radius: 4px;
                    opacity: 0.8;
                    transition: all 0.2s ease;
                    user-select: none;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    line-height: 1;
                    vertical-align: middle;
                    box-sizing: border-box;
                    z-index: 1;
                    outline: none;
                " onmouseover="this.style.opacity='1'; this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.opacity='0.8'; this.style.background='rgba(255,255,255,0.1)'">👁</button>
            </div>
        `;
    }

    // 绑定密码可见性切换事件
    function bindPasswordToggleEvent(inputId) {
        const toggleButton = document.getElementById(`${inputId}-toggle`);
        const passwordInput = document.getElementById(inputId);

        if (toggleButton && passwordInput) {
            // 切换密码可见性的函数
            const togglePasswordVisibility = () => {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    toggleButton.textContent = '🙈';
                    toggleButton.title = '隐藏密码';
                    logDebug(`${inputId}: 密码已显示`);
                } else {
                    passwordInput.type = 'password';
                    toggleButton.textContent = '👁';
                    toggleButton.title = '显示密码';
                    logDebug(`${inputId}: 密码已隐藏`);
                }
            };

            // 鼠标点击事件
            toggleButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                togglePasswordVisibility();
                // 保持输入框焦点
                passwordInput.focus();
            });

            // 键盘支持 - 按Enter或Space键切换
            toggleButton.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    e.stopPropagation();
                    togglePasswordVisibility();
                    // 保持输入框焦点
                    passwordInput.focus();
                }
            });

            // 设置初始状态
            toggleButton.title = '显示密码';
            toggleButton.setAttribute('tabindex', '0'); // 使按钮可以通过Tab键访问
            toggleButton.setAttribute('role', 'button');
            toggleButton.setAttribute('aria-label', '切换密码可见性');

            // 输入框获得焦点时的处理
            passwordInput.addEventListener('focus', () => {
                logDebug(`${inputId}: 输入框获得焦点`);
            });

            // 确保眼睛图标完美居中 - 针对不同输入框进行精确调整
            setTimeout(() => {
                // 获取输入框的实际高度和样式
                const inputHeight = passwordInput.offsetHeight;
                const computedStyle = window.getComputedStyle(passwordInput);
                const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
                const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
                const borderTop = parseFloat(computedStyle.borderTopWidth) || 0;
                const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 0;

                if (inputHeight > 0) {
                    // 计算内容区域的实际高度
                    const contentHeight = inputHeight - paddingTop - paddingBottom - borderTop - borderBottom;

                    // 确保按钮高度不超过输入框高度的80%，保持合适的比例
                    const maxButtonHeight = Math.min(28, inputHeight * 0.8);
                    toggleButton.style.height = `${maxButtonHeight}px`;
                    toggleButton.style.width = `${maxButtonHeight}px`;

                    // 针对不同输入框进行精确的垂直居中调整
                    let topOffset = '50%';
                    let transformValue = 'translateY(-50%)';

                    // 特别处理动态口令输入框（padding较小的情况）
                    if (inputId === 'dynamic-code' && paddingTop < 10) {
                        // 对于padding较小的输入框，稍微向下调整以与付费口令输入框对齐
                        const adjustmentOffset = (10 - paddingTop) * 0.5; // 根据padding差异进行微调
                        topOffset = `calc(50% + ${adjustmentOffset}px)`;
                        logDebug(`${inputId}: 应用动态口令特殊调整 - padding: ${paddingTop}px, 调整偏移: ${adjustmentOffset}px`);
                    }

                    // 应用计算出的位置
                    toggleButton.style.top = topOffset;
                    toggleButton.style.transform = transformValue;

                    logDebug(`${inputId}: 眼睛图标已调整 - 输入框高度: ${inputHeight}px, 内容高度: ${contentHeight}px, padding: ${paddingTop}px/${paddingBottom}px, 按钮尺寸: ${maxButtonHeight}px, 位置: ${topOffset}`);
                }
            }, 100);

            logDebug(`${inputId}: 密码切换功能已绑定`);
        } else {
            logDebug(`${inputId}: 未找到密码输入框或切换按钮`);
        }
    }

    // 测试密码切换功能和居中效果
    function testPasswordToggleFeature() {
        logDebug('开始测试密码切换功能和居中效果...');

        // 创建与实际使用相同的测试输入框
        const testConfigs = [
            {
                id: 'test-dynamic-code',
                style: 'width:100%;padding:8px;border-radius:4px;border:1px solid #ccc;margin-top:5px;background:#333;color:white;font-size:14px;',
                label: '动态口令输入框样式 (padding: 8px)'
            },
            {
                id: 'test-payment-code',
                style: 'width: 100%; padding: 10px; border: 1px solid #ffc107; border-radius: 4px; background: rgba(255,255,255,0.1); color: white; font-size: 14px; box-sizing: border-box;',
                label: '付费口令输入框样式 (padding: 10px)'
            },
            {
                id: 'test-small-input',
                style: 'width: 100%; padding: 6px; border: 1px solid #ccc; border-radius: 4px; background: #333; color: white; font-size: 12px;',
                label: '小尺寸输入框 (padding: 6px)'
            }
        ];

        // 创建测试容器
        const testContainer = document.createElement('div');
        testContainer.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10000;
            background: rgba(0,0,0,0.9);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #555;
            min-width: 280px;
            max-width: 320px;
        `;

        let testHtml = '<h4 style="color: white; margin: 0 0 10px 0; font-size: 14px;">眼睛图标居中对比测试</h4>';

        testConfigs.forEach((config, index) => {
            const inputHtml = createPasswordInputWithToggle(config.id, `测试密码 ${index + 1}`, config.style);
            testHtml += `
                <div style="margin-bottom: 15px;">
                    <label style="color: #ccc; font-size: 12px; display: block; margin-bottom: 5px;">${config.label}</label>
                    ${inputHtml}
                </div>
            `;
        });

        testHtml += '<p style="color: #999; font-size: 11px; margin: 10px 0 0 0;">对比眼睛图标位置是否一致，点击👁测试切换，15秒后自动移除</p>';

        testContainer.innerHTML = testHtml;
        document.body.appendChild(testContainer);

        // 绑定事件
        setTimeout(() => {
            testConfigs.forEach(config => {
                bindPasswordToggleEvent(config.id);
            });

            // 15秒后自动移除测试元素
            setTimeout(() => {
                if (document.body.contains(testContainer)) {
                    document.body.removeChild(testContainer);
                    logDebug('眼睛图标居中对比测试完成');
                }
            }, 15000);
        }, 200);

        logDebug('眼睛图标居中对比测试已添加到页面右上角，重点对比动态口令和付费口令输入框的图标位置');
    }

    // 调试函数：分析输入框和眼睛图标的位置信息
    function debugPasswordInputPositions() {
        logDebug('开始分析密码输入框和眼睛图标位置...');

        const inputIds = ['dynamic-code', 'payment-code-input'];

        inputIds.forEach(inputId => {
            const input = document.getElementById(inputId);
            const toggle = document.getElementById(`${inputId}-toggle`);

            if (input && toggle) {
                const inputRect = input.getBoundingClientRect();
                const toggleRect = toggle.getBoundingClientRect();
                const inputStyle = window.getComputedStyle(input);
                const toggleStyle = window.getComputedStyle(toggle);

                logDebug(`=== ${inputId} 位置分析 ===`);
                logDebug(`输入框: 高度=${input.offsetHeight}px, padding=${inputStyle.paddingTop}/${inputStyle.paddingBottom}, border=${inputStyle.borderTopWidth}/${inputStyle.borderBottomWidth}`);
                logDebug(`输入框位置: top=${inputRect.top}, bottom=${inputRect.bottom}, 中心=${inputRect.top + inputRect.height/2}`);
                logDebug(`眼睛图标: 高度=${toggle.offsetHeight}px, top=${toggleStyle.top}, transform=${toggleStyle.transform}`);
                logDebug(`眼睛图标位置: top=${toggleRect.top}, bottom=${toggleRect.bottom}, 中心=${toggleRect.top + toggleRect.height/2}`);
                logDebug(`垂直偏差: ${Math.abs((inputRect.top + inputRect.height/2) - (toggleRect.top + toggleRect.height/2)).toFixed(2)}px`);
                logDebug('');
            } else {
                logDebug(`${inputId}: 输入框或眼睛图标未找到`);
            }
        });
    }

    // 显示验证口令输入界面
    function showPaymentCodeInputPanel(type) {
        const title = type === 'windsurf' ? 'Windsurf' : 'Cursor';
        const passwordInputHtml = createPasswordInputWithToggle(
            'payment-code-input',
            '请输入验证口令',
            'width: 100%; padding: 10px; border: 1px solid #ffc107; border-radius: 4px; background: rgba(255,255,255,0.1); color: white; font-size: 14px; box-sizing: border-box;'
        );

        updateInfoPanel(`
            <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">🔐 输入验证口令</h3>
            <div style="margin: 15px 0; padding: 15px; background: rgba(255,193,7,0.2); border-radius: 8px; border-left: 4px solid #ffc107;">
                <p style="margin: 0 0 10px 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">生成${title}付费凭证需要验证口令</p>
                ${passwordInputHtml}
                <p style="margin: 10px 0 0 0; color: #ffecb3; font-size: 12px; text-align: center;">请联系作者获取验证口令</p>
            </div>
            <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                <button id="verify-payment-code" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4CAF50, #45a049) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">✅ 验证口令</button>
                <button id="cancel-verification" style="width:100%;padding:12px;background:linear-gradient(135deg, #757575, #616161) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">❌ 取消</button>
            </div>
        `);

        // 绑定密码切换事件
        setTimeout(() => {
            bindPasswordToggleEvent('payment-code-input');
        }, 100);

        // 绑定验证按钮事件
        document.getElementById('verify-payment-code')?.addEventListener('click', async () => {
            const inputCode = document.getElementById('payment-code-input')?.value?.trim();
            if (!inputCode) {
                updateInfoPanel(`
                    <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">⚠️ 请输入验证口令</h3>
                    <div style="margin: 15px 0; padding: 15px; background: rgba(255,152,0,0.2); border-radius: 8px; border-left: 4px solid #ff9800;">
                        <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">验证口令不能为空，请输入有效的验证口令</p>
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                        <button id="retry-input" style="width:100%;padding:12px;background:linear-gradient(135deg, #FF9800, #F57C00) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">🔄 重新输入</button>
                    </div>
                `);

                document.getElementById('retry-input')?.addEventListener('click', () => {
                    showPaymentCodeInputPanel(type);
                });
                return;
            }

            // 开始验证
            await handlePaymentVerification(type, inputCode);
        });

        // 绑定取消按钮事件
        document.getElementById('cancel-verification')?.addEventListener('click', () => {
            // 返回到原始的推广页面界面
            if (type === 'windsurf') {
                // 重新显示Windsurf推广界面
                updateInfoPanel(`
                    <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">🌊 骑Windsurf成为人上人</h3>
                    <div style="margin: 15px 0; padding: 15px; background: rgba(74,144,226,0.2); border-radius: 8px; border-left: 4px solid #4A90E2;">
                        <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">生成Windsurf付费凭证，通过审核后直接900次哦</p>
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                        <button id="generate-windsurf-payment-proof" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4A90E2, #357ABD) !important;color:white;border:none;border-radius:6px;font-size:14px;font-weight:bold;cursor:pointer;">🎫 生成Windsurf付费凭证</button>
                        <button id="back-to-profile" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                        <button id="contact-author" style="width:100%;padding:12px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">📱 联系作者</button>
                    </div>
                    <div id="wechat-qr-container" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                        <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                        <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                    </div>
                `);
                // 重新绑定生成按钮
                document.getElementById('generate-windsurf-payment-proof')?.addEventListener('click', () => {
                    showPaymentCodeInputPanel('windsurf');
                });

                // 重新绑定返回按钮
                document.getElementById('back-to-profile')?.addEventListener('click', () => {
                    window.location.href = 'https://app.augmentcode.com/account/subscription';
                });

                // 重新绑定联系作者按钮
                bindContactAuthorButton();

            } else {
                // 重新显示Cursor推广界面
                updateInfoPanel(`
                    <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">⚡ 骑Cursor成为人上人</h3>
                    <div style="margin: 15px 0; padding: 15px; background: rgba(108,123,127,0.2); border-radius: 8px; border-left: 4px solid #6C7B7F;">
                        <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">生成Cursor付费凭证，通过审核后直接900次哦</p>
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                        <button id="generate-cursor-payment-proof" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #6C7B7F, #4A5568) !important;color:white;border:none;border-radius:6px;font-size:14px;font-weight:bold;cursor:pointer;">🎫 生成Cursor付费凭证</button>
                        <button id="back-to-profile" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                        <button id="contact-author" style="width:100%;padding:12px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">📱 联系作者</button>
                    </div>
                    <div id="wechat-qr-container" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                        <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                        <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                    </div>
                `);
                // 重新绑定生成按钮
                document.getElementById('generate-cursor-payment-proof')?.addEventListener('click', () => {
                    showPaymentCodeInputPanel('cursor');
                });

                // 重新绑定返回按钮
                document.getElementById('back-to-profile')?.addEventListener('click', () => {
                    window.location.href = 'https://app.augmentcode.com/account/subscription';
                });

                // 重新绑定联系作者按钮
                bindContactAuthorButton();
            }
        });

        // 让输入框获得焦点
        setTimeout(() => {
            document.getElementById('payment-code-input')?.focus();
        }, 100);
    }

    // 显示协议确认面板
    function showTermsAcceptPanel() {
        logDebug('显示协议确认面板');

        // 确保面板存在并可见
        let panel = document.getElementById('cursor-auto-panel');
        if (!panel) {
            panel = addInfoPanel();
        } else {
            panel.style.display = 'block';
        }

        // 检查协议复选框状态
        const termsCheckbox = document.querySelector('#terms-of-service-checkbox') ||
                             document.querySelector('input[type="checkbox"]') ||
                             document.querySelector('[name*="terms"]') ||
                             document.querySelector('[id*="terms"]');

        const isTermsChecked = termsCheckbox?.checked || false;

        updateInfoPanel(`
            <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">🎉 注册即将完成！</h3>
            <div style="margin: 10px 0; color: white; font-size: 14px; line-height: 1.5;">
                <p style="margin: 8px 0; color: #4CAF50; font-weight: bold;">✅ 邮箱验证已通过</p>
                <p style="margin: 8px 0; color: #4CAF50; font-weight: bold;">✅ 密码设置已完成</p>
                <p style="margin: 8px 0; color: #FF9800; font-weight: bold;">📋 最后一步：接受服务条款</p>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 12px; border-radius: 6px; margin: 10px 0;">
                <p style="margin: 0; color: #fff; font-size: 13px; text-align: center;">
                    请在页面上勾选"我同意服务条款"复选框<br>
                    然后点击"Continue"按钮完成注册
                </p>
            </div>
            <div style="margin: 10px 0; padding: 10px; border-radius: 6px; background: ${isTermsChecked ? 'rgba(76,175,80,0.2)' : 'rgba(255,152,0,0.2)'}; border-left: 4px solid ${isTermsChecked ? '#4CAF50' : '#FF9800'};">
                <p style="margin: 0; color: #fff; font-size: 12px; text-align: center;">
                    ${isTermsChecked ? '✅ 协议已勾选，可以点击Continue按钮了' : '⚠️ 请勾选协议复选框'}
                </p>
            </div>
            <div style="margin-top: 15px; width: 100%;">
                <button id="go-to-dashboard" style="width: 100%; background: linear-gradient(135deg, #4776E6, #8E54E9) !important; color: white; border: none; border-radius: 4px; padding: 10px; font-size: 14px; cursor: pointer;">🚀 前往控制台</button>
            </div>
        `);

        // 前往控制台
        document.getElementById('go-to-dashboard')?.addEventListener('click', () => {
            window.open('https://app.augmentcode.com', '_blank');
        });

        // 监听协议复选框状态变化
        if (termsCheckbox) {
            const checkboxObserver = new MutationObserver(() => {
                // 延迟更新面板，避免频繁刷新
                setTimeout(() => {
                    showTermsAcceptPanel();
                }, 500);
            });

            checkboxObserver.observe(termsCheckbox, {
                attributes: true,
                attributeFilter: ['checked']
            });

            // 也监听点击事件
            termsCheckbox.addEventListener('change', () => {
                setTimeout(() => {
                    showTermsAcceptPanel();
                }, 500);
            });
        }
    }

    // 绑定联系作者按钮事件
    function bindContactAuthorButton() {
        document.getElementById('contact-author')?.addEventListener('click', () => {
            logDebug('点击联系作者按钮');
            const qrContainer = document.getElementById('wechat-qr-container');
            const contactBtn = document.getElementById('contact-author');

            if (qrContainer && contactBtn) {
                if (qrContainer.style.display === 'none' || qrContainer.style.display === '') {
                    // 展开二维码
                    qrContainer.style.display = 'block';
                    contactBtn.textContent = '📱 收起联系';
                    contactBtn.style.background = 'linear-gradient(135deg, #FF6B6B, #FF5252) !important';
                } else {
                    // 收起二维码
                    qrContainer.style.display = 'none';
                    contactBtn.textContent = '📱 联系作者';
                    contactBtn.style.background = 'linear-gradient(135deg, #07C160, #00A854) !important';
                }
            }
        });
    }

    // 处理付费验证
    async function handlePaymentVerification(type, userInputCode) {
        const title = type === 'windsurf' ? 'Windsurf' : 'Cursor';

        try {
            // 显示验证中状态
            updateInfoPanel(`
                <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">🔐 验证中...</h3>
                <div style="margin: 15px 0; padding: 15px; background: rgba(255,193,7,0.2); border-radius: 8px; border-left: 4px solid #ffc107;">
                    <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">正在验证您的口令，请稍候...</p>
                    <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #ffc107;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                    <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
                </div>
            `);

            // 调用验证接口
            logDebug(`开始验证付费口令，类型: ${type}，用户输入: "${userInputCode}"`);
            const verificationResult = await verifyPaymentCode(userInputCode);
            logDebug(`验证结果:`, verificationResult);

            if (verificationResult.success) {
                // 验证成功，显示生成中状态
                const bgColor = type === 'windsurf' ? 'rgba(74,144,226,0.2)' : 'rgba(76,175,80,0.2)';
                const borderColor = type === 'windsurf' ? '#4A90E2' : '#4caf50';

                updateInfoPanel(`
                    <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">🎫 正在生成${title}付费凭证</h3>
                    <div style="margin: 15px 0; padding: 15px; background: ${bgColor}; border-radius: 8px; border-left: 4px solid ${borderColor};">
                        <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">验证通过！正在生成您的${title}付费凭证...</p>
                        <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid ${borderColor};border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                        <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
                    </div>
                `);

                // 获取用户邮箱并生成PDF
                const userEmail = await getUserEmail();
                logDebug(`获取到用户邮箱: ${userEmail}`);

                // 根据类型生成对应的付费凭证
                if (type === 'windsurf') {
                    generateAndOpenWindsurfPDF(userEmail);
                } else if (type === 'cursor') {
                    generateAndOpenPDF(userEmail);
                }

                // 显示成功状态
                setTimeout(() => {
                    updateInfoPanel(`
                        <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">✅ ${title}付费凭证已生成</h3>
                        <div style="margin: 15px 0; padding: 15px; background: rgba(76,175,80,0.2); border-radius: 8px; border-left: 4px solid #4caf50;">
                            <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">付费凭证已在新窗口中打开</p>
                            <p style="margin: 10px 0 0 0; color: #fff; font-size: 12px; text-align: center;">请使用浏览器的打印功能保存为PDF</p>
                        </div>
                        <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                            <button id="generate-again-${type}" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4caf50, #45a049) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">🔄 重新生成</button>
                            <button id="back-to-profile" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                        </div>
                    `);

                    // 绑定重新生成按钮
                    document.getElementById(`generate-again-${type}`)?.addEventListener('click', () => {
                        showPaymentCodeInputPanel(type);
                    });

                    // 重新绑定返回按钮
                    document.getElementById('back-to-profile')?.addEventListener('click', () => {
                        window.location.href = 'https://app.augmentcode.com/account/subscription';
                    });
                }, 1000);

            } else {
                // 验证失败，显示错误信息
                updateInfoPanel(`
                    <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">❌ 验证失败</h3>
                    <div style="margin: 15px 0; padding: 15px; background: rgba(244,67,54,0.2); border-radius: 8px; border-left: 4px solid #f44336;">
                        <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">${verificationResult.message}</p>
                        <p style="margin: 10px 0 0 0; color: #ffcdd2; font-size: 12px; text-align: center;">请检查您的验证口令是否正确</p>
                    </div>
                    <div style="display: flex; justify-content: space-around; margin: 15px 0;">
                        <div style="text-align: center; margin: 0 5px;">
                            <img src="https://www.liubao.org.cn/image/wx.png" style="width: 100%; height: auto; max-width: 120px; display: block; margin: 0 auto; border-radius: 4px;">
                            <p style="margin: 5px 0; font-size: 12px; color: #fff;">微信</p>
                        </div>
                        <div style="text-align: center; margin: 0 5px;">
                            <img src="https://www.liubao.org.cn/image/zfb.png" style="width: 100%; height: auto; max-width: 120px; display: block; margin: 0 auto; border-radius: 4px;">
                            <p style="margin: 5px 0; font-size: 12px; color: #fff;">支付宝</p>
                        </div>
                    </div>
                    <p style="margin: 10px 0; font-size: 14px; color: #FFD700; text-align: center;">💡 联系作者获取正确的验证口令</p>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                        <button id="retry-input-${type}" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #FF9800, #F57C00) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">🔄 重新输入</button>
                        <button id="back-to-profile" style="width:100%;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                    </div>
                `);

                // 绑定重新输入按钮
                document.getElementById(`retry-input-${type}`)?.addEventListener('click', () => {
                    showPaymentCodeInputPanel(type);
                });

                // 绑定返回按钮
                document.getElementById('back-to-profile')?.addEventListener('click', () => {
                    window.location.href = 'https://app.augmentcode.com/account/subscription';
                });
            }

        } catch (error) {
            logDebug(`验证过程出错: ${error.message}`);
            updateInfoPanel(`
                <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">❌ 验证出错</h3>
                <div style="margin: 15px 0; padding: 15px; background: rgba(244,67,54,0.2); border-radius: 8px; border-left: 4px solid #f44336;">
                    <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">验证过程中出现错误</p>
                    <p style="margin: 10px 0 0 0; color: #ffcdd2; font-size: 12px; text-align: center;">${error.message}</p>
                </div>
                <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                    <button id="retry-verification-${type}" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #FF9800, #F57C00) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">🔄 重试验证</button>
                    <button id="back-to-profile" style="width:100%;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                </div>
            `);

            // 绑定重试按钮
            document.getElementById(`retry-verification-${type}`)?.addEventListener('click', () => {
                showPaymentCodeInputPanel(type);
            });

            // 绑定返回按钮
            document.getElementById('back-to-profile')?.addEventListener('click', () => {
                window.location.href = 'https://app.augmentcode.com/account/subscription';
            });
        }
    }

    // 处理升级按钮点击
    async function handleUpgradeClick(type) {
        try {
            // 检查是否在推广页面
            const isOnPromotionPage = window.location.href.includes('/promotions/');

            if (isOnPromotionPage) {
                // 在推广页面，显示验证中状态到面板
                updateInfoPanel(`
                    <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">🔐 验证付费权限</h3>
                    <div style="margin: 15px 0; padding: 15px; background: rgba(255,193,7,0.2); border-radius: 8px; border-left: 4px solid #ffc107;">
                        <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">正在验证您的付费权限，请稍候...</p>
                        <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #ffc107;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                        <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
                    </div>
                `);
            } else {
                // 不在推广页面，显示通知
                showNotification('正在验证付费权限，请稍候...', 'info');
            }

            // 验证付费口令
            const isVerified = await verifyPaymentCode();

            if (isVerified) {
                logDebug(`付费验证通过，开始生成${type}付费凭证`);

                if (isOnPromotionPage) {
                    // 在推广页面，更新面板显示生成中状态
                    const bgColor = type === 'windsurf' ? 'rgba(74,144,226,0.2)' : 'rgba(76,175,80,0.2)';
                    const borderColor = type === 'windsurf' ? '#4A90E2' : '#4caf50';
                    const title = type === 'windsurf' ? 'Windsurf' : 'Cursor';

                    updateInfoPanel(`
                        <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">🎫 正在生成${title}付费凭证</h3>
                        <div style="margin: 15px 0; padding: 15px; background: ${bgColor}; border-radius: 8px; border-left: 4px solid ${borderColor};">
                            <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">验证通过！正在生成您的${title}付费凭证...</p>
                            <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid ${borderColor};border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                            <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
                        </div>
                    `);
                } else {
                    showNotification('验证通过！正在生成付费凭证...', 'success');
                }

                // 获取用户邮箱
                const userEmail = await getUserEmail();

                // 根据类型生成对应的付费凭证
                if (type === 'windsurf') {
                    generateAndOpenWindsurfPDF(userEmail);
                } else if (type === 'cursor') {
                    generateAndOpenPDF(userEmail);
                }
            } else {
                logDebug('付费验证失败，显示提示信息');

                if (isOnPromotionPage) {
                    // 在推广页面，更新面板显示验证失败
                    updateInfoPanel(`
                        <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">💰 需要付费权限</h3>
                        <div style="margin: 15px 0; padding: 15px; background: rgba(244,67,54,0.2); border-radius: 8px; border-left: 4px solid #f44336;">
                            <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">生成付费凭证需要付费权限，请联系作者获取</p>
                        </div>
                        <div style="display: flex; justify-content: space-around; margin: 15px 0;">
                            <div style="text-align: center; margin: 0 5px;">
                                <img src="https://www.liubao.org.cn/image/wx.png" style="width: 100%; height: auto; max-width: 120px; display: block; margin: 0 auto; border-radius: 4px;">
                                <p style="margin: 5px 0; font-size: 12px; color: #fff;">微信</p>
                            </div>
                            <div style="text-align: center; margin: 0 5px;">
                                <img src="https://www.liubao.org.cn/image/zfb.png" style="width: 100%; height: auto; max-width: 120px; display: block; margin: 0 auto; border-radius: 4px;">
                                <p style="margin: 5px 0; font-size: 12px; color: #fff;">支付宝</p>
                            </div>
                        </div>
                        <p style="margin: 10px 0; font-size: 14px; color: #FFD700; text-align: center;">💡 联系作者获取付费权限</p>
                        <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                            <button id="back-to-profile" style="width:100%;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                        </div>
                    `);

                    // 重新绑定返回按钮
                    document.getElementById('back-to-profile')?.addEventListener('click', () => {
                        window.location.href = 'https://app.augmentcode.com/account/subscription';
                    });
                } else {
                    showNotification('付费验证失败，请联系作者获取付费权限', 'error');
                    showPaymentVerificationFailedPanel();
                }
            }
        } catch (error) {
            logDebug(`处理升级点击失败: ${error.message}`);

            if (window.location.href.includes('/promotions/')) {
                // 在推广页面显示错误
                updateInfoPanel(`
                    <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">❌ 验证失败</h3>
                    <div style="margin: 15px 0; padding: 15px; background: rgba(244,67,54,0.2); border-radius: 8px; border-left: 4px solid #f44336;">
                        <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">验证过程中出现错误</p>
                        <p style="margin: 10px 0 0 0; color: #ffcdd2; font-size: 12px; text-align: center;">${error.message}</p>
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                        <button id="retry-verification" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #FF9800, #F57C00) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">🔄 重试验证</button>
                        <button id="back-to-profile" style="width:100%;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                    </div>
                `);

                // 绑定重试按钮
                document.getElementById('retry-verification')?.addEventListener('click', async () => {
                    await handleUpgradeClick(type);
                });

                // 绑定返回按钮
                document.getElementById('back-to-profile')?.addEventListener('click', () => {
                    window.location.href = 'https://app.augmentcode.com/account/subscription';
                });
            } else {
                showNotification(`验证失败: ${error.message}`, 'error');
                showPaymentVerificationFailedPanel();
            }
        }
    }

    // 显示口令输入界面
    function showCodeInputPanel() {
        const passwordInputHtml = createPasswordInputWithToggle(
            'dynamic-code',
            '请输入动态口令',
            'width:100%;padding:8px;border-radius:4px;border:1px solid #ccc;margin-top:5px;background:#333;color:white;font-size:14px;'
        );

        updateInfoPanel(`
            <h3>动态口令验证</h3>
            <p>生成邮箱需要验证动态口令</p>
            <div style="margin-bottom:10px;">
                ${passwordInputHtml}
            </div>
            <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                <button id="verify-code" style="width:100%;margin-bottom:5px;background:#4CAF50 !important;">验证口令</button>
                <button id="back-to-main" style="width:100%;">返回主菜单</button>
            </div>
        `);

        // 绑定密码切换事件
        setTimeout(() => {
            bindPasswordToggleEvent('dynamic-code');
        }, 100);

        document.getElementById('verify-code')?.addEventListener('click', async () => {
            const inputCode = document.getElementById('dynamic-code')?.value;
            if (!inputCode) {
                showNotification('请输入动态口令', 'error');
                return;
            }

            try {
                const verificationResult = await verifyDynamicCode(inputCode);
                if (verificationResult.success) {
                    // 口令正确，继续生成邮箱
                    showNotification('口令验证成功');
                    generateEmailAfterVerification();
                } else {
                    // 口令错误，显示打赏界面
                    logDebug(`动态口令验证失败: ${verificationResult.message}`);
                    showCodeVerificationFailedPanel();
                }
            } catch (error) {
                showCodeVerificationFailedPanel();
            }
        });

        document.getElementById('back-to-main')?.addEventListener('click', () => {
            createMainPanel();
        });
    }

    // 显示口令验证失败界面
    function showCodeVerificationFailedPanel() {
        updateInfoPanel(`
            <h3>❌ 动态口令输入失败</h3>
            <p style="margin: 10px 0; color: #fff;">口令验证失败，请打赏后留言"获取口令"</p>
            <div style="display: flex; justify-content: space-around; margin: 10px 0;">
                <div style="text-align: center; margin: 0 5px;">
                    <img src="https://www.liubao.org.cn/image/wx.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                    <p style="margin: 5px 0; font-size: 12px; color: #fff;">微信</p>
                </div>
                <div style="text-align: center; margin: 0 5px;">
                    <img src="https://www.liubao.org.cn/image/zfb.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                    <p style="margin: 5px 0; font-size: 12px; color: #fff;">支付宝</p>
                </div>
            </div>
            <p style="margin: 10px 0; font-size: 14px; color: #FFD700; text-align: center;">💡 打赏时请留言"获取口令"</p>
            <p style="margin: 10px 0; font-size: 12px; color: #fff; text-align: center;">我们会在打赏留言中回复动态口令</p>
            <button id="retry-code" style="width:100%;margin-top:10px;background:#FF9800 !important;">重新输入口令</button>
            <button id="back-to-main" style="width:100%;margin-top:5px;">返回主菜单</button>
        `);

        // 添加悬停放大效果
        const style = document.createElement('style');
        style.id = 'tip-qrcode-style';
        style.textContent = `
            .tip-qrcode-img:hover {
                transform: scale(1.1);
                cursor: pointer;
            }
        `;
        if (!document.getElementById('tip-qrcode-style')) {
            document.head.appendChild(style);
        }

        document.getElementById('retry-code')?.addEventListener('click', () => {
            showCodeInputPanel();
        });

        document.getElementById('back-to-main')?.addEventListener('click', () => {
            createMainPanel();
        });
    }

    // 显示付费验证失败界面
    function showPaymentVerificationFailedPanel() {
        updateInfoPanel(`
            <h3>💰 付费验证失败</h3>
            <p style="margin: 10px 0; color: #fff;">需要付费权限才能生成凭证，请联系作者获取付费权限</p>
            <div style="display: flex; justify-content: space-around; margin: 10px 0;">
                <div style="text-align: center; margin: 0 5px;">
                    <img src="https://www.liubao.org.cn/image/wx.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                    <p style="margin: 5px 0; font-size: 12px; color: #fff;">微信</p>
                </div>
                <div style="text-align: center; margin: 0 5px;">
                    <img src="https://www.liubao.org.cn/image/zfb.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                    <p style="margin: 5px 0; font-size: 12px; color: #fff;">支付宝</p>
                </div>
            </div>
            <p style="margin: 10px 0; font-size: 14px; color: #FFD700; text-align: center;">💡 联系作者获取付费权限</p>
            <p style="margin: 10px 0; font-size: 12px; color: #fff; text-align: center;">打赏后留言"获取付费权限"即可开通</p>
            <button id="back-to-main-payment" style="width:100%;margin-top:10px;background:#4CAF50 !important;">返回主菜单</button>
        `);

        document.getElementById('back-to-main-payment')?.addEventListener('click', () => {
            createMainPanel();
        });
    }

    // 口令验证成功后生成邮箱
    async function generateEmailAfterVerification() {
        updateInfoPanel(`
            <h3>正在生成邮箱</h3>
            <p>口令验证成功，正在创建临时邮箱...</p>
            <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
            <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
        `);

        try {
            const accountInfo = await createNewAccount();
            updateInfoPanel(`
                <h3>邮箱生成成功</h3>
                <p><strong>邮箱：</strong> ${accountInfo.email}</p>
                <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                    <button id="fill-email-new" style="width:100%;margin-bottom:5px;background:#2196F3 !important;">复制邮箱</button>
                    <button id="back-to-main" style="width:100%;">返回主菜单</button>
                </div>
            `);

            document.getElementById('fill-email-new')?.addEventListener('click', () => {
                navigator.clipboard.writeText(accountInfo.email).then(() => {
                    showNotification('邮箱已复制');
                });
            });

            document.getElementById('back-to-main')?.addEventListener('click', () => {
                createMainPanel();
            });
        } catch (error) {
            updateInfoPanel(`
                <h3>邮箱生成失败</h3>
                <p>错误: ${error.message}</p>
                <button id="back-to-main" style="width:100%;margin-top:10px;">返回主菜单</button>
            `);

            document.getElementById('back-to-main')?.addEventListener('click', () => {
                createMainPanel();
            });
        }
    }



    // 查找元素 - 简化版
    function findElement(selectors) {
        if (typeof selectors === 'string') {
            selectors = [selectors];
        }

        // 依次尝试每个选择器
        for (const selector of selectors) {
            try {
                const element = document.querySelector(selector);
                if (element) {
                    logDebug(`找到元素: ${selector}`);
                    return element;
                }
            } catch (e) {
                logDebug(`选择器错误 ${selector}: ${e.message}`);
            }
        }

        // 如果以上选择器都找不到，尝试用属性值匹配查找(适用于input)
        if (selectors.some(s => s.includes('name=') || s.includes('placeholder='))) {
            const inputs = document.querySelectorAll('input');
            for (const input of inputs) {
                for (const selector of selectors) {
                    if (selector.includes('name=') && input.name === selector.split('name=')[1].replace(/["']/g, '')) {
                        logDebug(`通过name属性找到元素: ${input.name}`);
                        return input;
                    }
                    if (selector.includes('placeholder=') && input.placeholder === selector.split('placeholder=')[1].replace(/["']/g, '')) {
                        logDebug(`通过placeholder属性找到元素: ${input.placeholder}`);
                        return input;
                    }
                }
            }
        }

        logDebug(`未找到元素: ${selectors.join(', ')}`);
        return null;
    }

    // 设置输入框的值 - 简化版，只保留有效方法
    async function setInputValue(input, value) {
        if (!input) return false;

        logDebug(`尝试设置输入框值: ${value}`);

        // 方法1: 直接设置value + 触发事件 (在日志中已证明有效)
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));

        // 验证结果
        if (input.value === value) {
            logDebug(`成功设置输入框值: ${value}`);
            return true;
        } else {
            logDebug(`设置输入框值失败，当前值: ${input.value}, 期望值: ${value}`);
            return false;
        }
    }

    // 等待元素出现 - 简化版
    // 从TempMail获取验证码
    async function getTempMailVerificationCode(email) {
        logDebug(`开始获取临时邮箱验证码: ${email}`);

        const config = await getConfig();
        if (!config) {
            throw new Error('无法获取配置信息');
        }

        const [username, domain] = email.split('@');
        const apiUrl = config.tempEmailApi || 'https://mail.niubea.site';
        const pin = config.emailPin || '';
        const maxRetries = config.emailVerificationRetries || 5;
        const waitTime = config.emailVerificationWait || 10;
        const realEmailDomain = config.realEmailDomain || 'mail.niubea.site';

        logDebug(`邮箱配置: API=${apiUrl}, 用户名=${username}, 域名=${domain}, 实际域名=${realEmailDomain}, PIN=${pin}`);
        logDebug(`重试配置: 最大重试=${maxRetries}, 等待时间=${waitTime}秒`);

        // 获取存储的JWT令牌
        let jwt = null;
        try {
            jwt = await new Promise((resolve) => {
                chrome.storage.local.get('emailJwt', (result) => {
                    resolve(result.emailJwt || '');
                });
            });
            logDebug(`获取到JWT令牌: ${jwt ? (jwt.substring(0, 10) + '...') : '未找到'}`);
        } catch (error) {
            logDebug(`获取JWT令牌出错: ${error.message}`);
        }

        // 获取邮件列表
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
                // 请求邮件列表
                logDebug(`第${attempt + 1}次尝试获取邮件列表，请求参数：username=${username}, domain=${domain}, JWT=${jwt ? '已获取' : '未找到'}`);

                // 直接向API端点发送请求，确保JWT令牌正确传递
                const directApiUrl = `${apiUrl}/api/mails?limit=20&offset=0`;

                const mailListResponse = await chrome.runtime.sendMessage({
                    type: 'fetchData',
                    url: directApiUrl,
                    options: {
                        method: 'GET',
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": jwt ? `Bearer ${jwt}` : 'Bearer'
                        }
                    }
                });

                logDebug(`邮件列表API直接响应: ${JSON.stringify(mailListResponse).substring(0, 200)}...`);

                // 如果JWT无效，尝试重新获取JWT令牌
                if (!mailListResponse.success && mailListResponse.error && mailListResponse.error.includes('401')) {
                    logDebug('JWT认证失败，尝试创建新邮箱获取新JWT...');

                    // 创建新邮箱以获取新的JWT令牌
                    const createEmailResponse = await chrome.runtime.sendMessage({
                        type: 'createEmail',
                        apiUrl,
                        emailName: username
                    });

                    if (createEmailResponse.success && (createEmailResponse.data.jwt || createEmailResponse.data.token)) {
                        jwt = createEmailResponse.data.jwt || createEmailResponse.data.token;
                        logDebug(`已获取新的JWT令牌: ${jwt.substring(0, 10)}...`);

                        // 保存新的JWT令牌到本地存储
                        await new Promise((resolve) => {
                            chrome.storage.local.set({ emailJwt: jwt }, () => {
                                logDebug('已保存新的JWT令牌到本地存储');
                                resolve();
                            });
                        });

                        // 使用新JWT重新尝试获取邮件列表
                        const newMailListResponse = await chrome.runtime.sendMessage({
                            type: 'fetchData',
                            url: directApiUrl,
                            options: {
                                method: 'GET',
                                headers: {
                                    "Content-Type": "application/json",
                                    "Authorization": `Bearer ${jwt}`
                                }
                            }
                        });

                        logDebug(`使用新JWT重新获取邮件列表响应: ${JSON.stringify(newMailListResponse).substring(0, 200)}...`);

                        if (newMailListResponse.success) {
                            // 更新响应对象
                            mailListResponse.success = true;
                            mailListResponse.data = newMailListResponse.data;
                            mailListResponse.error = null;
                        }
                    }
                }

                if (!mailListResponse.success) {
                    throw new Error(mailListResponse.error || '获取邮件列表失败');
                }

                // 处理直接API返回的数据格式
                const mailListData = mailListResponse.data;

                // 从API响应中获取邮件列表 - 适应实际数据结构
                const emails = mailListData?.results || [];

                if (!emails || emails.length === 0) {
                    logDebug(`第${attempt + 1}次尝试: 未找到邮件 (API直接返回数据: ${JSON.stringify(mailListData)})`);

                    // 尝试使用后台API作为备用方法
                    logDebug(`尝试使用后台fetchMailList作为备用方法`);

                    const backupResponse = await chrome.runtime.sendMessage({
                        type: 'fetchMailList',
                        apiUrl,
                        username,
                        domain,
                        realEmailDomain,
                        pin
                    });

                    logDebug(`后台fetchMailList响应: ${JSON.stringify(backupResponse).substring(0, 200)}...`);

                    // 检查后台API返回
                    if (backupResponse.success && backupResponse.data) {
                        // 尝试获取邮件从不同可能的结构
                        const backupEmails = backupResponse.data.results ||
                                            backupResponse.data.mails ||
                                            (backupResponse.data.result === true && []) ||
                                            [];

                        if (backupEmails && backupEmails.length > 0) {
                            logDebug(`通过备用方法找到 ${backupEmails.length} 封邮件`);
                            // 处理通过备用方法找到的邮件
                            return processFoundEmails(backupEmails, apiUrl, username, domain, realEmailDomain, pin);
                        }
                    }

                    if (attempt < maxRetries - 1) {
                        logDebug(`等待${waitTime}秒后重试...`);
                        await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
                        continue;
                    }
                    throw new Error('未找到邮件');
                }

                logDebug(`成功直接从API获取到 ${emails.length} 封邮件，处理中...`);
                return processFoundEmails(emails, apiUrl, username, domain, realEmailDomain, pin);

            } catch (error) {
                logDebug(`获取验证码出错 (${attempt + 1}/${maxRetries}): ${error.message}`);
                if (attempt < maxRetries - 1) {
                    logDebug(`等待${waitTime}秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
                    continue;
                }
                throw error;
            }
        }

        throw new Error(`未能获取验证码，已尝试${maxRetries}次`);
    }

    // 处理找到的邮件列表，提取验证码
    async function processFoundEmails(emails, apiUrl, username, domain, realEmailDomain, pin) {
        try {
            // 使用邮件列表中的第一封邮件（最新的邮件）
            logDebug(`开始处理 ${emails.length} 封邮件中的第一封`);
            const latestMail = emails[0];
            const mailId = latestMail.id;

            if (!mailId) {
                logDebug(`无效的邮件ID，API返回: ${JSON.stringify(latestMail)}`);
                throw new Error('无效的邮件ID');
            }

            logDebug(`获取到最新邮件ID: ${mailId}`);

            // 检查邮件是否已经包含完整内容
            if (latestMail.raw) {
                logDebug('邮件已包含完整内容，无需再次请求详情');

                // 从邮件内容中提取验证码
                const mailRaw = latestMail.raw;
                const mailText = mailRaw || '';
                const mailHtml = mailRaw || '';

                logDebug(`邮件文本前200个字符: ${mailText.substring(0, 200)}`);

                // 多种模式匹配验证码
                // 1. 星号包围的验证码格式 (优先级最高，Cursor邮件通常使用此格式)
                const starCodePattern = /\*{3,}[\r\n\s]*(\d{6})[\r\n\s]*\*{3,}/;
                const starMatch = mailText.match(starCodePattern);

                // 2. HTML中的验证码 <h1>123456</h1>
                const htmlCodePattern = /<h1[^>]*>(\d{6})<\/h1>/i;
                const htmlMatch = mailHtml.match(htmlCodePattern);

                // 3. 特殊格式 "verification code: 123456" 或 "code is: 123456"
                const labeledCodePattern = /(?:verification code|code is|your code|验证码)(?:\s*[:：]\s*)(\d{6})/i;
                const labeledMatch = mailText.match(labeledCodePattern);

                // 4. 标准6位数字验证码
                const codePattern = /\b(\d{6})\b/g;
                const codeMatches = Array.from(mailText.matchAll(codePattern));

                let code = null;

                if (starMatch && starMatch[1]) {
                    code = starMatch[1];
                    logDebug(`通过星号格式找到验证码: ${code}`);
                } else if (htmlMatch && htmlMatch[1]) {
                    code = htmlMatch[1];
                    logDebug(`通过HTML标签找到验证码: ${code}`);
                } else if (labeledMatch && labeledMatch[1]) {
                    code = labeledMatch[1];
                    logDebug(`通过标签文本找到验证码: ${code}`);
                } else if (codeMatches && codeMatches.length > 0) {
                    // 使用最后一个匹配的6位数字，因为通常验证码在邮件内容后面
                    code = codeMatches[codeMatches.length - 1][1];
                    logDebug(`通过标准格式找到验证码: ${code}`);
                }

                if (code) {
                    logDebug(`成功获取验证码: ${code}`);

                    // 获取验证码后删除邮件
                    try {
                        logDebug(`尝试删除已读邮件: ${mailId}`);
                        const deleteResponse = await chrome.runtime.sendMessage({
                            type: 'deleteMailById',
                            apiUrl,
                            mailId,
                            username,
                            domain,
                            realEmailDomain,
                            pin
                        });

                        if (deleteResponse.success) {
                            logDebug('邮件删除成功');
                        } else {
                            logDebug(`邮件删除失败: ${deleteResponse.error || '未知错误'}`);
                        }
                    } catch (deleteError) {
                        logDebug(`邮件删除过程中发生错误: ${deleteError.message}`);
                    }

                    return code;
                }
            }

            logDebug(`无法从邮件内容中提取验证码`);
            throw new Error('无法从邮件内容中提取验证码');
        } catch (error) {
            logDebug(`处理邮件列表过程中出错: ${error.message}`);
            throw error;
        }
    }

    // 从邮箱获取验证码
    async function getVerificationCode(email) {
        try {
            logDebug(`开始获取验证码, 邮箱: ${email}`);

            if (!email) {
                logDebug('邮箱地址为空，无法获取验证码');
                return null;
            }

            const emailDomain = email.split('@')[1];

            // 获取配置信息，用于判断域名
            const config = await getConfig();
            const configDomain = config?.emailDomain || defaultConfig.emailDomain;
            logDebug(`配置的邮箱域名: ${configDomain}, 当前邮箱域名: ${emailDomain}`);

            // 尝试使用新的专用获取验证码功能 - 优先使用此方法
            try {
                logDebug('尝试使用专用API获取验证码');
                const code = await getTempMailVerificationCode(email);
                if (code) {
                    logDebug(`成功通过专用API获取验证码: ${code}`);
                    // 添加自动填充功能
                    addQuickFill(code);
                    return code;
                } else {
                    logDebug('专用API未返回验证码，尝试其他方法');
                }
            } catch (specialError) {
                logDebug(`使用专用API获取验证码失败: ${specialError.message}，尝试备用方法`);
            }

            // 备用方法：使用特定域名API或通用方法
            if (emailDomain === configDomain || email.includes('niubea.site')) {
                logDebug(`使用 ${configDomain} API获取验证码（备用方法）`);
                try {
                    // 获取tempMailAccount中存储的JWT令牌
                    const tempMailAccount = await new Promise((resolve) => {
                        chrome.storage.local.get('tempMailAccount', (result) => {
                            resolve(result.tempMailAccount || {});
                        });
                    });

                    let jwt = tempMailAccount?.jwt || null;

                    // 如果tempMailAccount中没有jwt，尝试从emailJwt中获取（向后兼容）
                    if (!jwt) {
                        await new Promise((resolve) => {
                            chrome.storage.local.get('emailJwt', (result) => {
                                jwt = result.emailJwt || '';
                                resolve();
                            });
                        });
                    }

                    logDebug(`JWT令牌状态: ${jwt ? '已获取' : '未找到'}`);

                    // 只发送一次请求，直接使用JWT令牌
                    if (!jwt) {
                        logDebug('没有找到JWT令牌，无法获取邮件');
                        return null;
                    }

                    // 从配置中获取API端点
                    let apiEndpoint = config?.tempEmailApi || defaultConfig.tempEmailApi || 'https://mail.niubea.site';
                    if (!apiEndpoint.startsWith('http')) {
                        apiEndpoint = `https://${apiEndpoint}`;
                    }

                    // 使用后台脚本获取邮件列表
                    logDebug(`通过后台脚本获取邮件列表: ${apiEndpoint}`);
                    const response = await fetchMailsViaBackground(apiEndpoint, jwt);

                    logDebug(`邮件API响应: ${JSON.stringify(response).substring(0, 200)}...`);

                    // 邮件列表可能在response.results或其他字段
                    const emails = response?.results || response?.data?.results || [];

                    if (!emails || emails.length === 0) {
                        logDebug('邮件列表为空，未找到验证码');
                        return null;
                    }

                    logDebug(`成功获取到 ${emails.length} 封邮件，开始解析验证码`);

                    // 处理邮件内容，查找验证码
                    for (const mail of emails) {
                        if (mail.raw) {
                            logDebug(`处理邮件，主题: ${mail.subject || '无主题'}, 内容前100字符: ${mail.raw.substring(0, 100)}`);

                            // 星号包围的验证码格式 (优先级最高，Cursor邮件通常使用此格式)
                            const starCodePattern = /\*{3,}[\r\n\s]*(\d{6})[\r\n\s]*\*{3,}/;
                            const starMatch = mail.raw.match(starCodePattern);
                            if (starMatch && starMatch[1]) {
                                const verificationCode = starMatch[1];
                                logDebug(`从星号包围的文本中找到验证码: ${verificationCode}`);
                                return verificationCode;
                            }

                            // HTML中的验证码 <h1>123456</h1>
                            const htmlCodePattern = /<h1[^>]*>(\d{6})<\/h1>/i;
                            const htmlMatch = mail.raw.match(htmlCodePattern);
                            if (htmlMatch && htmlMatch[1]) {
                                const verificationCode = htmlMatch[1];
                                logDebug(`从HTML标签中找到验证码: ${verificationCode}`);
                                return verificationCode;
                            }

                            // 一般性检查 - 查找"verify your email"后面附近的6位数字
                            const verifyPattern = /verify your email[^]*?(\d{6})/i;
                            const verifyMatch = mail.raw.match(verifyPattern);
                            if (verifyMatch && verifyMatch[1]) {
                                const verificationCode = verifyMatch[1];
                                logDebug(`从"verify your email"附近找到验证码: ${verificationCode}`);
                                return verificationCode;
                            }

                            // 查找任何6位数字验证码
                            const digitsPattern = /\b(\d{6})\b/g;
                            const digitsMatches = [...mail.raw.matchAll(digitsPattern)];
                            if (digitsMatches.length > 0) {
                                // 使用最后一个匹配的6位数字，因为通常验证码在邮件内容后面
                                const verificationCode = digitsMatches[digitsMatches.length - 1][0];
                                logDebug(`从raw中找到6位验证码: ${verificationCode}`);
                                return verificationCode;
                            }
                        }
                    }

                    logDebug('未在邮件中找到验证码');
                    return null;
                } catch (error) {
                    logDebug(`${configDomain} API调用失败: ${error.message}`);
                    console.error(`${configDomain} API调用失败详情:`, error);
                    throw new Error(`邮箱API调用失败(${configDomain}): ${error.message}`);
                }
            } else {
                // 原有的邮箱验证码获取逻辑，用于其他域名
                logDebug(`使用默认API获取验证码(非${configDomain}域名)`);
                return new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage(
                        { type: 'getMailVerifyCode', email },
                        (response) => {
                            if (chrome.runtime.lastError) {
                                logDebug(`获取验证码出错: ${chrome.runtime.lastError.message}`);
                                reject(new Error(chrome.runtime.lastError.message));
                                return;
                            }

                            if (response.error) {
                                logDebug(`获取验证码API返回错误: ${response.error}`);
                                reject(new Error(response.error));
                                return;
                            }

                            const verifyCode = response.verifyCode;
                            if (verifyCode) {
                                logDebug(`成功获取验证码: ${verifyCode}`);

                                // 添加自动填充功能
                                addQuickFill(verifyCode);

                                resolve(verifyCode);
                            } else {
                                logDebug('API未返回验证码');
                                resolve(null);
                            }
                        }
                    );
                });
            }
        } catch (error) {
            logDebug(`获取验证码处理出错: ${error.message}`);
            console.error('获取验证码处理出错:', error);
            return null;
        }
    }



    // 查找提交按钮
    function findSubmitButton() {
        // 优先查找Augment Code助手页面的Continue按钮
        const registerContinueButton = document.querySelector('button.flex.items-center.justify-center.rounded-lg.bg-brand-dark');
        if (registerContinueButton && registerContinueButton.textContent.includes('Continue')) {
            logDebug('找到Augment Code助手页面的Continue按钮');
            return registerContinueButton;
        }
        
        // 常见的提交按钮选择器
        const selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:contains("Verify")',
            'button:contains("Submit")',
            'button:contains("Continue")',
            'button:contains("Next")',
            'button:contains("确认")',
            'button:contains("提交")',
            'button:contains("验证")',
            'button.rt-Button'
        ];

        for (const selector of selectors) {
            try {
                const buttons = document.querySelectorAll(selector);
                if (buttons.length > 0) {
                    logDebug(`通过选择器 ${selector} 找到提交按钮`);
                    return buttons[0];
                }
            } catch (e) {
                logDebug(`选择器 ${selector} 查询错误: ${e.message}`);
            }
        }

        // 如果上述选择器都未找到，尝试获取所有按钮
        const allButtons = document.querySelectorAll('button');
        logDebug(`页面共有 ${allButtons.length} 个按钮`);

        // 查找最有可能的提交按钮（通常是表单下最后一个按钮）
        const forms = document.querySelectorAll('form');
        for (const form of forms) {
            const formButtons = form.querySelectorAll('button');
            if (formButtons.length > 0) {
                logDebug(`在表单中找到 ${formButtons.length} 个按钮，使用最后一个`);
                return formButtons[formButtons.length - 1];
            }
        }

        // 如果没有在表单中找到按钮，查找页面上所有主要按钮
        const primaryButtons = Array.from(allButtons).filter(btn => {
            const style = window.getComputedStyle(btn);
            const text = btn.textContent.toLowerCase();

            // 检查按钮样式和文本来判断是否为主要按钮
            return (
                (style.backgroundColor && style.backgroundColor !== 'transparent' && style.backgroundColor !== 'rgba(0, 0, 0, 0)') ||
                text.includes('submit') || text.includes('verify') ||
                text.includes('confirm') || text.includes('continue') ||
                text.includes('提交') || text.includes('验证') || text.includes('确认')
            );
        });

        if (primaryButtons.length > 0) {
            logDebug(`找到 ${primaryButtons.length} 个可能的提交按钮，使用第一个`);
            return primaryButtons[0];
        }

        logDebug('未找到提交按钮');
        return null;
    }

    // 添加快速填充验证码功能
    function addQuickFill(code) {
        // 实现快速填充验证码的功能
        try {
            logDebug('添加验证码快速填充功能');
            // 这里可以实现一个悬浮按钮让用户一键填充验证码
        } catch (e) {
            logDebug(`添加验证码填充功能出错: ${e.message}`);
        }
    }

    // 从后台获取邮件列表 - 预留函数
    async function fetchMailsViaBackground(apiEndpoint, jwt) {
        logDebug(`通过后台获取邮件列表: ${apiEndpoint}`);

        try {
            // 构建邮件列表API端点
            const mailsApiUrl = `${apiEndpoint}/api/mails?limit=20&offset=0`;
            logDebug(`请求邮件列表API: ${mailsApiUrl}`);

            // 通过background.js发送请求
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    type: "fetchData",
                    url: mailsApiUrl,
                    options: {
                        method: "GET",
                        headers: {
                            "Authorization": jwt,
                            "Content-Type": "application/json"
                        }
                    }
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }

                    if (response.error) {
                        reject(new Error(response.error));
                        return;
                    }

                    resolve(response);
                });
            });

            logDebug(`邮件列表API响应成功，获取到 ${response?.data?.results?.length || 0} 封邮件`);
            return response.data;
        } catch (error) {
            logDebug(`获取邮件列表失败: ${error.message}`);
            throw error;
        }
    }

    // 创建主面板
    function createMainPanel() {
        logDebug('创建主面板');

        // 检查是否已有面板，如果有则直接显示
        let panel = document.getElementById('cursor-auto-panel');
        if (panel) {
            panel.style.display = 'block';
            showButton.style.display = 'none';

            // 清除现有内容并重新创建
            const contentContainer = panel.querySelector('.panel-content');
            if (contentContainer) {
                contentContainer.innerHTML = '';
            }
        } else {
            // 否则创建新面板
            panel = addInfoPanel();
        }

        // 判断当前页面URL
        const currentUrl = window.location.href.toLowerCase();

        // 优先检查是否在 Augment Code terms-accept 页面
        if (currentUrl.includes('auth.augmentcode.com/terms-accept')) {
            logDebug('检测到 Augment Code 协议接受页面，显示协议处理界面');
            showTermsAcceptPanel();
            return panel;
        }

        // 如果不是 Augment Code 相关页面，显示通用面板
        if (!currentUrl.includes('augmentcode.com')) {
            logDebug('非 Augment Code 页面，显示通用面板');

            updateInfoPanel(`
                <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #fff; text-align: center;">👋 Augment Code 注册助手</h3>
                <p style="margin: 10px 0 15px 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.4;">欢迎使用 Augment Code 注册助手！<br>点击下方按钮前往注册页面</p>

                <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                    <button id="go-to-augmentcode" style="width:100%;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;margin-bottom:8px;">🚀 前往 Augment Code</button>
                    <button id="go-to-register" style="width:100%;padding:12px;background:linear-gradient(135deg, #11998e, #38ef7d) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;margin-bottom:8px;">📝 前往注册页面</button>
                    <button id="contact-author-general" style="width:100%;padding:12px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;margin-bottom:8px;">📱 联系作者</button>
                </div>

                <div id="wechat-qr-container-general" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                    <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                    <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                </div>

                <p style="margin: 15px 0 5px 0; font-size: 14px; color: #fff; text-align: center;">如果本工具对您有帮助，欢迎打赏支持作者~</p>
                <div style="display: flex; justify-content: space-around; margin: 10px 0;">
                    <div style="text-align: center; margin: 0 5px;">
                        <img src="https://www.liubao.org.cn/image/wx.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 120px; display: block; margin: 0 auto; border-radius: 4px;">
                        <p style="margin: 8px 0 0 0; font-size: 12px; color: #fff;">微信打赏</p>
                    </div>
                    <div style="text-align: center; margin: 0 5px;">
                        <img src="https://www.liubao.org.cn/image/zfb.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 120px; display: block; margin: 0 auto; border-radius: 4px;">
                        <p style="margin: 8px 0 0 0; font-size: 12px; color: #fff;">支付宝打赏</p>
                    </div>
                </div>
                <p style="margin: 10px 0 0 0; font-size: 12px; color: #FFD700; text-align: center;">💡 扫码打赏并留言获取专属口令</p>
            `);

            // 添加悬停放大效果
            const style = document.createElement('style');
            style.id = 'tip-qrcode-style';
            style.textContent = `
                .tip-qrcode-img:hover {
                    transform: scale(1.5);
                    z-index: 10000;
                }
            `;
            if (!document.getElementById('tip-qrcode-style')) {
                document.head.appendChild(style);
            }

            // 前往 Augment Code 按钮
            document.getElementById('go-to-augmentcode')?.addEventListener('click', () => {
                window.open('https://www.augmentcode.com/', '_blank');
            });

            // 前往注册页面按钮
            document.getElementById('go-to-register')?.addEventListener('click', () => {
                window.open('https://login.augmentcode.com/u/login/identifier', '_blank');
            });

            // 添加联系作者按钮功能
            document.getElementById('contact-author-general')?.addEventListener('click', () => {
                logDebug('点击通用面板联系作者按钮');
                const qrContainer = document.getElementById('wechat-qr-container-general');
                const contactBtn = document.getElementById('contact-author-general');

                if (qrContainer && contactBtn) {
                    if (qrContainer.style.display === 'none') {
                        // 展开二维码
                        qrContainer.style.display = 'block';
                        contactBtn.textContent = '📱 收起联系';
                        contactBtn.style.background = 'linear-gradient(135deg, #FF6B6B, #FF5252) !important';
                    } else {
                        // 收起二维码
                        qrContainer.style.display = 'none';
                        contactBtn.textContent = '📱 联系作者';
                        contactBtn.style.background = 'linear-gradient(135deg, #07C160, #00A854) !important';
                    }
                }
            });

            return panel;
        }

        // 检查页面是否包含注册成功的标识文字
        const pageText = document.body.innerText || '';
        const isRegistrationSuccess = pageText.includes('Switching back to');

        if (isRegistrationSuccess) {
            logDebug('检测到注册成功标识"Switching back to"，显示注册成功页面');

            updateInfoPanel(`
                <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #fff; text-align: center;">🎉 注册成功！</h3>
                <p style="margin: 10px 0 15px 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.4;">恭喜您成功注册Augment Code！<br>如果本工具对您有帮助，欢迎打赏支持作者~</p>
                <div style="display: flex; justify-content: space-around; margin: 15px 0;">
                    <div style="text-align: center; margin: 0 5px;">
                        <img src="https://www.liubao.org.cn/image/wx.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 120px; display: block; margin: 0 auto; border-radius: 4px;">
                        <p style="margin: 8px 0 0 0; font-size: 12px; color: #fff;">微信打赏</p>
                    </div>
                    <div style="text-align: center; margin: 0 5px;">
                        <img src="https://www.liubao.org.cn/image/zfb.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 120px; display: block; margin: 0 auto; border-radius: 4px;">
                        <p style="margin: 8px 0 0 0; font-size: 12px; color: #fff;">支付宝打赏</p>
                    </div>
                </div>
                <p style="margin: 10px 0 0 0; font-size: 12px; color: #FFD700; text-align: center;">💡 扫码打赏并留言获取专属口令</p>
                <div style="margin-top:15px;">
                    <!-- 并排显示的按钮组 -->
                    <div style="display:flex;gap:8px;margin-bottom:8px;">
                        <button id="download-reset-pack-success" style="flex:1;padding:12px;background:linear-gradient(135deg, #FF6B6B, #FF8E53) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">📦 下载重置包</button>
                        <button id="go-to-profile" style="flex:1;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">前往个人中心</button>
                    </div>
                    <!-- 垂直显示的其他按钮 -->
                    <button id="contact-author-success" style="width:100%;padding:12px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;margin-bottom:8px;">📱 联系作者</button>
                    <button id="back-to-home" style="width:100%;padding:12px;background:linear-gradient(135deg, #11998e, #38ef7d) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;margin-bottom:8px;">回到首页</button>
                </div>

                <div id="wechat-qr-container-success" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                    <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                    <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                </div>
            `);

            // 添加悬停放大效果
            const style = document.createElement('style');
            style.id = 'tip-qrcode-style';
            style.textContent = `
                .tip-qrcode-img:hover {
                    transform: scale(1.5);
                    z-index: 10000;
                }
            `;
            if (!document.getElementById('tip-qrcode-style')) {
                document.head.appendChild(style);
            }

            // 下载重置包按钮
            document.getElementById('download-reset-pack-success')?.addEventListener('click', () => {
                window.open('https://www.123684.com/s/sOUKVv-DAmxh', '_blank');
            });

            // 前往个人中心按钮
            document.getElementById('go-to-profile')?.addEventListener('click', () => {
                window.location.href = 'https://app.augmentcode.com/account/subscription';
            });

            // 回到首页按钮
            document.getElementById('back-to-home')?.addEventListener('click', () => {
                window.location.href = 'https://www.augmentcode.com/';
            });

            // 添加联系作者按钮功能
            document.getElementById('contact-author-success')?.addEventListener('click', () => {
                logDebug('点击注册成功页面联系作者按钮');
                const qrContainer = document.getElementById('wechat-qr-container-success');
                const contactBtn = document.getElementById('contact-author-success');

                if (qrContainer && contactBtn) {
                    if (qrContainer.style.display === 'none') {
                        // 展开二维码
                        qrContainer.style.display = 'block';
                        contactBtn.textContent = '📱 收起联系';
                        contactBtn.style.background = 'linear-gradient(135deg, #FF6B6B, #FF5252) !important';
                    } else {
                        // 收起二维码
                        qrContainer.style.display = 'none';
                        contactBtn.textContent = '📱 联系作者';
                        contactBtn.style.background = 'linear-gradient(135deg, #07C160, #00A854) !important';
                    }
                }
            });

            return panel;
        }

        // 检查是否在 Augment Code 个人中心页面
        if (currentUrl.includes('app.augmentcode.com/account/subscription')) {
            logDebug('检测到 Augment Code 个人中心页面，显示升级SVIP和打赏界面');

            updateInfoPanel(`
                <h3 style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; color: #fff;">👋 感谢使用Augment Code助手</h3>

                <div style="margin: 10px 0; border-top: 1px solid rgba(255,255,255,0.2); padding-top: 10px;">
                    <button id="upgrade-windsurf-svip" style="width: 100%; margin-bottom: 8px; background: linear-gradient(135deg, #4A90E2, #357ABD) !important; color: #fff; border: none; border-radius: 6px; padding: 12px; font-size: 14px; font-weight: bold; cursor: pointer;">👊 拳打Windsurf，一键成神！</button>
                    <button id="upgrade-cursor-svip" style="width: 100%; margin-bottom: 8px; background: linear-gradient(135deg, #6C7B7F, #4A5568) !important; color: #fff; border: none; border-radius: 6px; padding: 12px; font-size: 14px; font-weight: bold; cursor: pointer;">🦵 脚踢Cursor，代码飞起！</button>
                    <button id="contact-author-profile" style="width: 100%; margin-bottom: 10px; background: linear-gradient(135deg, #07C160, #00A854) !important; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer; padding: 12px;">📱 联系作者</button>
                </div>

                <div id="wechat-qr-container-profile" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                    <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                    <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                </div>

                <p style="margin: 15px 0 5px 0; font-size: 14px; color: #fff; border-top: 1px solid rgba(255,255,255,0.2); padding-top: 15px;">如果本工具对您有帮助，欢迎打赏支持作者~</p>
                <div style="display: flex; justify-content: space-around; margin: 10px 0;">
                    <div style="text-align: center; margin: 0 5px;">
                        <img src="https://www.liubao.org.cn/image/wx.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                        <p style="margin: 5px 0; font-size: 12px; color: #fff;">微信</p>
                    </div>
                    <div style="text-align: center; margin: 0 5px;">
                        <img src="https://www.liubao.org.cn/image/zfb.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                        <p style="margin: 5px 0; font-size: 12px; color: #fff;">支付宝</p>
                    </div>
                </div>
                <p style="margin: 10px 0 0 0; font-size: 12px; color: #FFD700; text-align: center;">💡 扫码打赏并留言获取专属口令</p>

                <p style="margin: 10px 0; font-size: 12px; color: #fff; text-align: center;">感谢您使用Augment Code注册助手！</p>
            `);

            // 添加悬停放大效果
            const style = document.createElement('style');
            style.id = 'tip-qrcode-style';
            style.textContent = `
                .tip-qrcode-img:hover {
                    transform: scale(1.5);
                    z-index: 10000;
                }
                #contact-author-profile:hover {
                    transform: scale(1.05);
                    transition: transform 0.3s ease;
                }
                #contact-author-profile {
                    transition: transform 0.3s ease;
                }
            `;
            if (!document.getElementById('tip-qrcode-style')) {
                document.head.appendChild(style);
            }

            // 添加升级SVIP按钮功能
            document.getElementById('upgrade-windsurf-svip')?.addEventListener('click', () => {
                logDebug('点击拳打Windsurf按钮，跳转到Windsurf推广页面');
                window.location.href = 'https://app.augmentcode.com/promotions/windsurf';
            });

            document.getElementById('upgrade-cursor-svip')?.addEventListener('click', () => {
                logDebug('点击脚踢Cursor按钮，跳转到Cursor推广页面');
                window.location.href = 'https://app.augmentcode.com/promotions/cursor';
            });

            // 添加联系作者按钮功能
            document.getElementById('contact-author-profile')?.addEventListener('click', () => {
                logDebug('点击个人中心页面联系作者按钮');
                const qrContainer = document.getElementById('wechat-qr-container-profile');
                const contactBtn = document.getElementById('contact-author-profile');

                if (qrContainer && contactBtn) {
                    if (qrContainer.style.display === 'none') {
                        // 展开二维码
                        qrContainer.style.display = 'block';
                        contactBtn.textContent = '📱 收起联系';
                        contactBtn.style.background = 'linear-gradient(135deg, #FF6B6B, #FF5252) !important';
                    } else {
                        // 收起二维码
                        qrContainer.style.display = 'none';
                        contactBtn.textContent = '📱 联系作者';
                        contactBtn.style.background = 'linear-gradient(135deg, #07C160, #00A854) !important';
                    }
                }
            });

            return panel;
        }

        // 检查是否在 Augment Code Cursor推广页面
        if (currentUrl.includes('app.augmentcode.com/promotions/cursor')) {
            logDebug('检测到 Augment Code Cursor推广页面，显示推广信息');

            // 确保面板存在并可见
            if (!panel) {
                panel = document.createElement('div');
                panel.id = 'cursor-auto-panel';
                panel.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 320px;
                    background: rgba(0, 0, 0, 0.9);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 20px;
                    z-index: 2147483647;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    color: white;
                    font-size: 14px;
                    line-height: 1.4;
                    display: block;
                `;
                document.body.appendChild(panel);
            }

            // 确保面板可见
            panel.style.display = 'block';

            updateInfoPanel(`
                <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">⚡ 骑Cursor成为人上人</h3>
                <div style="margin: 15px 0; padding: 15px; background: rgba(108,123,127,0.2); border-radius: 8px; border-left: 4px solid #6C7B7F;">
                    <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">生成Cursor付费凭证，通过审核后直接900次哦</p>
                </div>
                <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                    <button id="generate-cursor-payment-proof" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #6C7B7F, #4A5568) !important;color:white;border:none;border-radius:6px;font-size:14px;font-weight:bold;cursor:pointer;">🎫 生成Cursor付费凭证</button>
                    <button id="back-to-profile" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                    <button id="contact-author" style="width:100%;padding:12px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">📱 联系作者</button>
                </div>
                <div id="wechat-qr-container" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                    <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                    <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                </div>
            `);

            // 绑定生成Cursor付费凭证按钮事件
            document.getElementById('generate-cursor-payment-proof')?.addEventListener('click', () => {
                logDebug('点击生成Cursor付费凭证按钮，显示验证口令输入界面');
                showPaymentCodeInputPanel('cursor');
            });

            // 绑定返回个人中心按钮事件
            document.getElementById('back-to-profile')?.addEventListener('click', () => {
                logDebug('点击返回个人中心按钮');
                window.location.href = 'https://app.augmentcode.com/account/subscription';
            });

            // 绑定联系作者按钮事件
            bindContactAuthorButton();

            return panel;
        }

        // 检查是否在 Augment Code Windsurf推广页面
        if (currentUrl.includes('app.augmentcode.com/promotions/windsurf')) {
            logDebug('检测到 Augment Code Windsurf推广页面，显示推广信息');

            // 确保面板存在并可见
            if (!panel) {
                panel = document.createElement('div');
                panel.id = 'cursor-auto-panel';
                panel.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 320px;
                    background: rgba(0, 0, 0, 0.9);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 20px;
                    z-index: 2147483647;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    color: white;
                    font-size: 14px;
                    line-height: 1.4;
                    display: block;
                `;
                document.body.appendChild(panel);
            }

            // 确保面板可见
            panel.style.display = 'block';

            updateInfoPanel(`
                <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">🌊 骑Windsurf成为人上人</h3>
                <div style="margin: 15px 0; padding: 15px; background: rgba(74,144,226,0.2); border-radius: 8px; border-left: 4px solid #4A90E2;">
                    <p style="margin: 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.5;">生成Windsurf付费凭证，通过审核后直接900次哦</p>
                </div>
                <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                    <button id="generate-windsurf-payment-proof" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4A90E2, #357ABD) !important;color:white;border:none;border-radius:6px;font-size:14px;font-weight:bold;cursor:pointer;">🎫 生成Windsurf付费凭证</button>
                    <button id="back-to-profile" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">返回个人中心</button>
                    <button id="contact-author" style="width:100%;margin-bottom:8px;padding:12px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">📱 联系作者</button>
                </div>
                <div id="wechat-qr-container" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                    <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                    <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                </div>
            `);

            // 绑定生成Windsurf付费凭证按钮事件
            document.getElementById('generate-windsurf-payment-proof')?.addEventListener('click', () => {
                logDebug('点击生成Windsurf付费凭证按钮，显示验证口令输入界面');
                showPaymentCodeInputPanel('windsurf');
            });

            // 绑定返回个人中心按钮事件
            document.getElementById('back-to-profile')?.addEventListener('click', () => {
                logDebug('点击返回个人中心按钮');
                window.location.href = 'https://app.augmentcode.com/account/subscription';
            });

            // 绑定联系作者按钮事件
            bindContactAuthorButton();

            return panel;
        }



        // 检查是否在 Augment Code 登录页面
        else if (currentUrl.includes('login.augmentcode.com')) {
            logDebug('检测到 Augment Code 登录页面，显示邮箱填充界面');

            // 检测具体的URL状态
            const isIdentifierPage = currentUrl.includes('/u/login/identifier');
            const isVerificationChallengePage = currentUrl.includes('/u/login/passwordless-email-challenge');

            logDebug(`URL状态检测: identifier=${isIdentifierPage}, challenge=${isVerificationChallengePage}`);

            // 获取账号信息，显示在浮窗中
            chrome.runtime.sendMessage({ type: "getAccountInfo" }, async (response) => {
                const accountInfo = response?.accountInfo || {};

                if (isIdentifierPage) {
                    // identifier页面：优先显示邮箱生成和复制功能
                    // 尝试从页面提取当前邮箱
                    const emailFromPage = extractEmailFromPage();
                    let displayEmail = emailFromPage || accountInfo.email || '未生成';

                    // 如果从页面提取到了邮箱且与存储的不同，更新存储的账号信息
                    if (emailFromPage && accountInfo.email !== emailFromPage) {
                        chrome.runtime.sendMessage({
                            type: "setAccountInfo",
                            accountInfo: { ...accountInfo, email: emailFromPage }
                        });
                    }

                    // 判断是否有可用邮箱
                    const hasValidEmail = displayEmail && displayEmail !== '未生成';

                    updateInfoPanel(`
                        <h3>Augment Code 注册助手</h3>
                        <p>检测到邮箱填写页面</p>
                        <div style="margin-bottom:10px;">
                            <div style="display:flex;align-items:center;gap:8px;margin-bottom:10px;">
                                <span style="color:#fff;font-size:14px;"><strong>邮箱：</strong></span>
                                <span style="color:#4CAF50;font-size:14px;flex:1;word-break:break-all;">${displayEmail}</span>
                                ${hasValidEmail ? `<button id="copy-email" style="background:#FF9800 !important;color:white;border:none;border-radius:4px;padding:6px 12px;font-size:12px;cursor:pointer;white-space:nowrap;">📋 复制</button>` : ''}
                            </div>
                        </div>
                        <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                            <button id="generate-email" style="width:100%;margin-bottom:5px;background:#4CAF50 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">🎯 生成新邮箱地址</button>
                            <button id="get-verification-code" style="width:100%;margin-bottom:5px;background:#9E9E9E !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;opacity:0.5;cursor:not-allowed;" disabled>🔐 获取验证码</button>
                            <button id="contact-author-identifier" style="width:100%;margin-bottom:5px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">📱 联系作者</button>
                        </div>

                        <div id="wechat-qr-container-identifier" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                            <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                            <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                        </div>

                        <p style="margin-top:10px;font-size:12px;color:#666;text-align:center;">💡 请先生成邮箱，然后填写到页面中</p>
                    `);

                    // 添加identifier页面联系作者按钮事件监听器
                    setTimeout(() => {
                        document.getElementById('contact-author-identifier')?.addEventListener('click', () => {
                            logDebug('点击identifier页面联系作者按钮');
                            const qrContainer = document.getElementById('wechat-qr-container-identifier');
                            if (qrContainer) {
                                if (qrContainer.style.display === 'none' || qrContainer.style.display === '') {
                                    qrContainer.style.display = 'block';
                                    logDebug('展开identifier页面微信二维码');
                                } else {
                                    qrContainer.style.display = 'none';
                                    logDebug('收起identifier页面微信二维码');
                                }
                            } else {
                                logDebug('未找到identifier页面微信二维码容器');
                            }
                        });
                    }, 100);

                } else if (isVerificationChallengePage) {
                    // challenge页面：高亮获取验证码按钮，置灰其他按钮
                    // 尝试从页面提取当前邮箱
                    const emailFromPage = extractEmailFromPage();
                    let displayEmail = emailFromPage || accountInfo.email || '未知';

                    // 如果从页面提取到了邮箱且与存储的不同，更新存储的账号信息
                    if (emailFromPage && accountInfo.email !== emailFromPage) {
                        chrome.runtime.sendMessage({
                            type: "setAccountInfo",
                            accountInfo: { ...accountInfo, email: emailFromPage }
                        });
                    }

                    updateInfoPanel(`
                        <h3>Augment Code 注册助手</h3>
                        <p>检测到验证码输入页面</p>
                        <div style="margin-bottom:10px;">
                            <p><strong>邮箱：</strong> ${displayEmail}</p>
                        </div>
                        <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                            <button id="generate-email" class="disabled" style="width:100%;margin-bottom:5px;">生成新邮箱地址</button>
                            <button id="fill-email" class="disabled" style="width:100%;margin-bottom:5px;">复制邮箱</button>
                            <button id="get-verification-code" class="highlighted" style="width:100%;margin-bottom:5px;">获取验证码</button>
                            <button id="contact-author-challenge" style="width:100%;margin-bottom:5px;background:linear-gradient(135deg, #07C160, #00A854) !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">📱 联系作者</button>
                        </div>

                        <div id="wechat-qr-container-challenge" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                            <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                            <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                        </div>
                    `);

                    // 添加challenge页面联系作者按钮事件监听器
                    setTimeout(() => {
                        document.getElementById('contact-author-challenge')?.addEventListener('click', () => {
                            logDebug('点击challenge页面联系作者按钮');
                            const qrContainer = document.getElementById('wechat-qr-container-challenge');
                            if (qrContainer) {
                                if (qrContainer.style.display === 'none' || qrContainer.style.display === '') {
                                    qrContainer.style.display = 'block';
                                    logDebug('展开challenge页面微信二维码');
                                } else {
                                    qrContainer.style.display = 'none';
                                    logDebug('收起challenge页面微信二维码');
                                }
                            } else {
                                logDebug('未找到challenge页面微信二维码容器');
                            }
                        });
                    }, 100);

                    // 在challenge页面自动触发获取验证码（延迟执行，确保按钮事件已绑定）
                    setTimeout(() => {
                        if (displayEmail && displayEmail !== '未知') {
                            logDebug('challenge页面自动触发获取验证码');
                            document.getElementById('get-verification-code')?.click();
                        } else {
                            logDebug('challenge页面邮箱地址为空，尝试从页面提取邮箱');
                            // 尝试从页面提取邮箱地址
                            const extractedEmail = extractEmailFromPage();
                            if (extractedEmail) {
                                logDebug(`从页面提取到邮箱: ${extractedEmail}`);
                                // 更新账号信息
                                chrome.runtime.sendMessage({
                                    type: "setAccountInfo",
                                    accountInfo: { ...accountInfo, email: extractedEmail }
                                }, () => {
                                    // 更新成功后触发获取验证码
                                    setTimeout(() => {
                                        document.getElementById('get-verification-code')?.click();
                                    }, 500);
                                });
                            } else {
                                logDebug('无法从页面提取邮箱地址，等待用户手动操作');
                            }
                        }
                    }, 1000);
                } else {
                    // 默认页面布局 - 统一使用邮箱生成优先的界面
                    // 尝试从页面提取当前邮箱
                    const emailFromPage = extractEmailFromPage();
                    let displayEmail = emailFromPage || accountInfo.email || '未生成';

                    // 如果从页面提取到了邮箱且与存储的不同，更新存储的账号信息
                    if (emailFromPage && accountInfo.email !== emailFromPage) {
                        chrome.runtime.sendMessage({
                            type: "setAccountInfo",
                            accountInfo: { ...accountInfo, email: emailFromPage }
                        });
                    }

                    // 判断是否有可用邮箱
                    const hasValidEmail = displayEmail && displayEmail !== '未生成';

                    updateInfoPanel(`
                        <h3>Augment Code 注册助手</h3>
                        <p>检测到 Augment Code 登录页面</p>
                        <div style="margin-bottom:10px;">
                            <div style="display:flex;align-items:center;gap:8px;margin-bottom:10px;">
                                <span style="color:#fff;font-size:14px;"><strong>邮箱：</strong></span>
                                <span style="color:#4CAF50;font-size:14px;flex:1;word-break:break-all;">${displayEmail}</span>
                                ${hasValidEmail ? `<button id="copy-email" style="background:#FF9800 !important;color:white;border:none;border-radius:4px;padding:6px 12px;font-size:12px;cursor:pointer;white-space:nowrap;">📋 复制</button>` : ''}
                            </div>
                        </div>
                        <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                            <button id="generate-email" style="width:100%;margin-bottom:5px;background:#4CAF50 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">🎯 生成邮箱地址</button>
                            <button id="get-verification-code" style="width:100%;margin-bottom:5px;background:#9E9E9E !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;opacity:0.5;cursor:not-allowed;" disabled>🔐 获取验证码</button>
                        </div>
                        <p style="margin-top:10px;font-size:12px;color:#666;text-align:center;">💡 请先生成邮箱，然后填写到页面中</p>
                    `);

                }

                // 复制邮箱按钮 - 通用处理函数
                const handleCopyEmail = () => {
                    // 重新获取当前邮箱信息
                    const currentEmailFromPage = extractEmailFromPage();
                    const emailToCopy = currentEmailFromPage || accountInfo.email;

                    logDebug(`尝试复制邮箱: ${emailToCopy}`);

                    if (emailToCopy && emailToCopy !== '未生成' && emailToCopy !== '未知') {
                        // 使用现代的 Clipboard API
                        if (navigator.clipboard && navigator.clipboard.writeText) {
                            navigator.clipboard.writeText(emailToCopy).then(() => {
                                logDebug(`邮箱复制成功: ${emailToCopy}`);
                                // 临时更改按钮文本显示复制成功
                                const copyBtn = document.getElementById('copy-email');
                                if (copyBtn) {
                                    const originalText = copyBtn.textContent;
                                    copyBtn.textContent = '✅ 已复制';
                                    copyBtn.style.background = '#4CAF50 !important';
                                    setTimeout(() => {
                                        copyBtn.textContent = originalText;
                                        copyBtn.style.background = '#FF9800 !important';
                                    }, 2000);
                                }
                                showNotification(`邮箱已复制: ${emailToCopy}`);
                            }).catch(err => {
                                logDebug('复制邮箱失败:', err);
                                // 降级到传统方法
                                fallbackCopyToClipboard(emailToCopy);
                            });
                        } else {
                            // 降级到传统方法
                            fallbackCopyToClipboard(emailToCopy);
                        }
                    } else {
                        logDebug('没有可复制的邮箱地址');
                        showNotification('没有可复制的邮箱地址', 'warning');
                    }
                };

                // 降级复制方法
                const fallbackCopyToClipboard = (text) => {
                    try {
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();

                        const successful = document.execCommand('copy');
                        document.body.removeChild(textArea);

                        if (successful) {
                            logDebug(`邮箱复制成功(降级方法): ${text}`);
                            const copyBtn = document.getElementById('copy-email');
                            if (copyBtn) {
                                const originalText = copyBtn.textContent;
                                copyBtn.textContent = '✅ 已复制';
                                copyBtn.style.background = '#4CAF50 !important';
                                setTimeout(() => {
                                    copyBtn.textContent = originalText;
                                    copyBtn.style.background = '#FF9800 !important';
                                }, 2000);
                            }
                            showNotification(`邮箱已复制: ${text}`);
                        } else {
                            throw new Error('execCommand copy failed');
                        }
                    } catch (err) {
                        logDebug('降级复制方法也失败:', err);
                        showNotification('复制失败，请手动复制邮箱地址', 'error');
                    }
                };

                // 绑定复制邮箱按钮事件
                document.getElementById('copy-email')?.addEventListener('click', handleCopyEmail);

                // 生成邮箱地址按钮
                document.getElementById('generate-email')?.addEventListener('click', async () => {
                    // 显示口令输入界面
                    showCodeInputPanel();
                });



                // 获取验证码按钮
                document.getElementById('get-verification-code')?.addEventListener('click', async () => {
                    // 检查按钮是否被禁用
                    if (document.getElementById('get-verification-code')?.classList.contains('disabled')) {
                        logDebug('获取验证码按钮被禁用，忽略点击');
                        return;
                    }

                    if (isVerificationChallengePage) {
                        // challenge页面自动获取验证码并填充
                        updateInfoPanel(`
                            <h3>正在获取验证码</h3>
                            <p>正在从邮箱获取验证码...</p>
                            <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                        `);

                        try {
                            // 检查邮箱地址是否存在
                            let emailToUse = accountInfo.email;
                            if (!emailToUse) {
                                logDebug('账号信息中邮箱为空，尝试从页面提取');
                                const extractedEmail = extractEmailFromPage();
                                if (extractedEmail) {
                                    logDebug(`从页面提取到邮箱: ${extractedEmail}`);
                                    emailToUse = extractedEmail;
                                    // 更新账号信息
                                    await new Promise((resolve) => {
                                        chrome.runtime.sendMessage({
                                            type: "setAccountInfo",
                                            accountInfo: { ...accountInfo, email: extractedEmail }
                                        }, resolve);
                                    });
                                } else {
                                    throw new Error('无法获取邮箱地址，请先生成邮箱');
                                }
                            }

                            const code = await getVerificationCode(emailToUse);
                            if (code) {
                                updateInfoPanel(`
                                    <h3>验证码获取成功</h3>
                                    <p>为邮箱 ${emailToUse} 获取的验证码:</p>
                                    <p style="font-size:24px;font-weight:bold;text-align:center;margin:10px 0;letter-spacing:5px;color:#4CAF50;">${code}</p>
                                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                                        <button id="copy-code-challenge" style="width:100%;margin-bottom:5px;background:#4CAF50 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">📋 复制验证码</button>
                                        <button id="retry-get-code-challenge" style="width:100%;margin-bottom:5px;background:#FF9800 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">🔄 重新获取验证码</button>
                                        <button id="back-to-main-challenge" style="width:100%;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">返回主菜单</button>
                                    </div>
                                `);

                                // 添加按钮事件处理
                                document.getElementById('copy-code-challenge')?.addEventListener('click', () => {
                                    navigator.clipboard.writeText(code).then(() => {
                                        showNotification('验证码已复制');
                                    });
                                });

                                document.getElementById('retry-get-code-challenge')?.addEventListener('click', () => {
                                    document.getElementById('get-verification-code')?.click();
                                });

                                document.getElementById('back-to-main-challenge')?.addEventListener('click', () => {
                                    createMainPanel();
                                });
                            } else {
                                updateInfoPanel(`
                                    <h3>验证码获取失败</h3>
                                    <p>未能获取到验证码，请稍后重试</p>
                                    <button id="retry-get-code" style="width:100%;margin-top:10px;background:#FF9800 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">重试获取</button>
                                    <button id="back-to-main" style="width:100%;margin-top:5px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">返回主菜单</button>
                                `);



                                document.getElementById('retry-get-code')?.addEventListener('click', () => {
                                    document.getElementById('get-verification-code')?.click();
                                });

                                document.getElementById('back-to-main')?.addEventListener('click', () => {
                                    createMainPanel();
                                });
                            }
                        } catch (error) {
                            updateInfoPanel(`
                                <h3>验证码获取出错</h3>
                                <p>错误: ${error.message}</p>
                                <button id="back-to-main" style="width:100%;margin-top:10px;">返回主菜单</button>
                            `);



                            document.getElementById('back-to-main')?.addEventListener('click', () => {
                                createMainPanel();
                            });
                        }
                    } else {
                        // 其他页面使用原有逻辑
                        triggerGetVerificationCode(true);
                    }
                });
            });

            return panel;
        }
        
        // 先检查页面内容是否是验证码页面，这比URL检查更可靠
        if (checkIfVerificationPage()) {
            logDebug('检测到验证码页面(通过页面内容检测)，显示验证码填充界面');
            // 邮箱验证页面 - 更新为更直观的界面
            chrome.runtime.sendMessage({ type: "getAccountInfo" }, async (response) => {
                const accountInfo = response?.accountInfo || {};

                // 首先尝试从页面提取当前邮箱
                const emailFromPage = extractEmailFromPage();
                let displayEmail = emailFromPage || accountInfo.email || '未知';

                // 如果从页面提取到了邮箱且与存储的不同，更新存储的账号信息
                if (emailFromPage && accountInfo.email !== emailFromPage) {
                    logDebug(`页面邮箱(${emailFromPage})与存储邮箱(${accountInfo.email || '无'})不一致，更新账号信息`);
                    chrome.runtime.sendMessage({
                        type: "setAccountInfo",
                        accountInfo: { ...accountInfo, email: emailFromPage }
                    });
                }

                updateInfoPanel(`
                    <h3>获取验证码</h3>
                    <p>检测到您在验证码页面</p>
                    <div style="margin-bottom:10px;">
                        <div style="display:flex;align-items:center;gap:8px;margin-bottom:10px;">
                            <span style="color:#fff;font-size:14px;"><strong>邮箱：</strong></span>
                            <span style="color:#4CAF50;font-size:14px;flex:1;word-break:break-all;">${displayEmail}</span>
                        </div>
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                        <button id="get-verification-code" style="width:100%;margin-bottom:5px;background:#4CAF50 !important;">🔐 获取验证码</button>
                        <button id="back-to-main" style="width:100%;">返回主菜单</button>
                    </div>
                `);



                document.getElementById('get-verification-code')?.addEventListener('click', async () => {
                    // 手动触发获取验证码流程，但不自动填充
                    triggerGetVerificationCode(false);
                });

                document.getElementById('back-to-main')?.addEventListener('click', () => {
                    createMainPanel();
                });
            });

            return panel;
        }
        
        // 登录页面优先处理逻辑 - 明确检查登录页面并优先处理
        if (currentUrl.includes('/account/login')) {
            logDebug('检测到登录页面，显示登录和打赏内容');
            
            // 获取账号信息，显示在浮窗中
            chrome.runtime.sendMessage({ type: "getAccountInfo" }, async (response) => {
                const accountInfo = response?.accountInfo || {};
                const hasAccountInfo = accountInfo.email && accountInfo.password;
                
                // 整合打赏面板内容到主面板，同时添加登录功能
                updateInfoPanel(`
                    <h3 style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; color: #fff;">👋 欢迎使用Augment Code助手</h3>

                    <p style="margin: 10px 0 5px 0; font-size: 14px; color: #fff;">如果本工具对您有帮助，欢迎打赏支持作者~</p>
                    <div style="display: flex; justify-content: space-around; margin: 10px 0;">
                        <div style="text-align: center; margin: 0 5px;">
                            <img src="https://www.liubao.org.cn/image/wx.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                            <p style="margin: 5px 0; font-size: 12px; color: #fff;">微信</p>
                        </div>
                        <div style="text-align: center; margin: 0 5px;">
                            <img src="https://www.liubao.org.cn/image/zfb.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                            <p style="margin: 5px 0; font-size: 12px; color: #fff;">支付宝</p>
                        </div>
                    </div>
                    <p style="margin: 10px 0 0 0; font-size: 12px; color: #FFD700; text-align: center;">💡 扫码打赏并留言获取专属口令</p>

                    <div style="margin: 10px 0; color: white; font-size: 14px; border-top: 1px solid rgba(255,255,255,0.2); padding-top: 10px;">
                        ${hasAccountInfo ? `
                            <p style="margin: 5px 0; color: #fff;">您已有可用账号:</p>
                            <p style="margin: 5px 0; font-size: 13px; word-break: break-all; color: #fff;">📧 ${accountInfo.email}</p>
                            <p style="margin: 5px 0; font-size: 13px; color: #fff;">🔑 ${accountInfo.password || '未设置密码'}</p>
                        ` : `
                            <p style="margin: 5px 0; color: #fff;">您还没有已保存的账号</p>
                            <p style="margin: 5px 0; font-size: 13px; color: #fff;">可以选择注册新账号或登录已有账号</p>
                        `}
                    </div>

                    <div style="margin-top: 10px; width: 100%;">
                        <button id="auto-login" style="width: 100%; margin-bottom: 5px; background: linear-gradient(135deg, #11998e, #38ef7d) !important; ${!hasAccountInfo ? 'opacity: 0.5;' : ''}">使用已有账号登录</button>
                        <button id="go-to-register" style="width: 100%; margin-bottom: 5px; background: linear-gradient(135deg, #4776E6, #8E54E9) !important;">前往注册 Augment Code</button>
                        <button id="contact-author-main" style="width: 100%; margin-bottom: 5px; background: linear-gradient(135deg, #07C160, #00A854) !important; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer; padding: 12px;">📱 联系作者</button>
                    </div>

                    <div id="wechat-qr-container-main" style="display:none;margin-top:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                        <img src="https://liubao.org.cn/image/addwx.jpg" class="contact-qrcode-img" style="transition: transform 0.3s ease; width:100%;max-width:130px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                        <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                    </div>
                `);


                
                // 添加悬停放大效果
                const style = document.createElement('style');
                style.id = 'main-panel-qrcode-style';
                style.textContent = `
                    .tip-qrcode-img:hover {
                        transform: scale(1.5);
                        z-index: 10000;
                    }
                    .contact-qrcode-img:hover {
                        transform: scale(1.5);
                        z-index: 10000;
                        cursor: pointer;
                    }
                `;
                if (!document.getElementById('main-panel-qrcode-style')) {
                    document.head.appendChild(style);
                }
                
                // 添加前往注册按钮功能
                document.getElementById('go-to-register')?.addEventListener('click', () => {
                    window.location.href = 'https://login.augmentcode.com/u/login/identifier';
                });
                
                // 添加自动登录按钮功能
                document.getElementById('auto-login')?.addEventListener('click', () => {
                    if (hasAccountInfo) {
                        autoFillLoginForm();
                    } else {
                        showNotification('请先注册账号以获取登录信息', 'warning');
                    }
                });

                // 添加联系作者按钮功能
                document.getElementById('contact-author-main')?.addEventListener('click', () => {
                    logDebug('点击主面板联系作者按钮');
                    const qrContainer = document.getElementById('wechat-qr-container-main');
                    const contactBtn = document.getElementById('contact-author-main');

                    if (qrContainer && contactBtn) {
                        if (qrContainer.style.display === 'none') {
                            // 展开二维码
                            qrContainer.style.display = 'block';
                            contactBtn.textContent = '📱 收起联系';
                            contactBtn.style.background = 'linear-gradient(135deg, #FF6B6B, #FF5252) !important';
                        } else {
                            // 收起二维码
                            qrContainer.style.display = 'none';
                            contactBtn.textContent = '📱 联系作者';
                            contactBtn.style.background = 'linear-gradient(135deg, #07C160, #00A854) !important';
                        }
                    }
                });
            });
            
            return panel;
        }
        
        // 如果是注册页面，显示注册相关功能
        if (currentUrl.includes('/account/register')) {
            // 判断当前页面类型
            // 首先检查是否在密码设置页面（通过检测密码输入框）
            const passwordInput = findElement(['input[type="password"]', 'input[name="password"]', 'input[placeholder*="password" i]', 'input[placeholder*="Create password" i]']);
            const passwordConfirmInput = findElement(['input[name="confirmPassword"]', 'input[id="passwordConfirmation"]', 'input[placeholder*="Confirm password" i]']);

            if (passwordInput && passwordConfirmInput) {
                // 密码设置页面
                chrome.runtime.sendMessage({ type: "getAccountInfo" }, async (response) => {
                    const accountInfo = response?.accountInfo || {};

                    // 尝试从页面提取当前邮箱
                    const emailFromPage = extractEmailFromPage();
                    let displayEmail = emailFromPage || accountInfo.email || '未知';

                    // 如果从页面提取到了邮箱且与存储的不同，更新存储的账号信息
                    if (emailFromPage && accountInfo.email !== emailFromPage) {
                        chrome.runtime.sendMessage({
                            type: "setAccountInfo",
                            accountInfo: { ...accountInfo, email: emailFromPage }
                        });
                    }

                    updateInfoPanel(`
                        <h3>密码设置</h3>
                        <p>检测到您正在设置密码</p>
                        <div style="margin-bottom:10px;">
                            <p><strong>邮箱：</strong> ${displayEmail}</p>
                            <p><strong>密码：</strong> ${accountInfo.password || '未知'}</p>
                        </div>
                        <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                            <button id="copy-password" style="width:100%;margin-bottom:5px;">复制密码</button>
                            <button id="generate-new-password" style="width:100%;">生成新密码</button>
                            <button id="back-to-main" style="width:100%;">返回主菜单</button>
                        </div>
                    `);



                    // 绑定按钮事件
                    document.getElementById('copy-password')?.addEventListener('click', () => {
                        navigator.clipboard.writeText(accountInfo.password).then(() => {
                            showNotification('密码已复制');
                        });
                    });

                    document.getElementById('generate-new-password')?.addEventListener('click', () => {
                        const newPassword = generateRandomPassword();
                        // 更新账号信息中的密码
                        const updatedAccountInfo = {...accountInfo, password: newPassword};

                        chrome.runtime.sendMessage({
                            type: "saveAccountInfo",
                            email: updatedAccountInfo.email,
                            password: updatedAccountInfo.password
                        }, () => {
                            logDebug(`更新密码: ${newPassword}`);

                            // 显示新密码
                            updateInfoPanel(`
                                <h3>已生成新密码</h3>
                                <div style="margin-bottom:10px;">
                                    <p><strong>新密码：</strong> ${newPassword}</p>
                                </div>
                                <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                                    <button id="copy-new-password" style="width:100%;margin-bottom:5px;">复制密码</button>
                                    <button id="back-to-main-new" style="width:100%;">返回主菜单</button>
                                </div>
                            `);

                            document.getElementById('copy-new-password')?.addEventListener('click', () => {
                                navigator.clipboard.writeText(newPassword).then(() => {
                                    showNotification('新密码已复制');
                                });
                            });

                            document.getElementById('back-to-main-new')?.addEventListener('click', () => {
                                createMainPanel();
                            });
                        });
                    });

                    document.getElementById('back-to-main')?.addEventListener('click', () => {
                        createMainPanel();
                    });


                });
            } else {
                // 注册页面
                updateInfoPanel(`
                    <h3>Augment Code 注册助手</h3>
                    <p>检测到您正在注册 Augment Code 账号</p>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                        <button id="fill-signup" style="width:100%;margin-bottom:5px;">使用已有账号填写</button>
                        <button id="generate-account" style="width:100%;">生成新账号填写</button>
                    </div>
                `);



                // 绑定按钮事件
                document.getElementById('fill-signup')?.addEventListener('click', () => {
                    // 使用已有账号填写
                    getAccountInfo().then(info => {
                        autoFillSignupForm(info);
                    });
                });

                document.getElementById('generate-account')?.addEventListener('click', () => {
                    // 生成全新账号并填写
                    createNewAccount().then(info => {
                        autoFillSignupForm(info);
                    });
                });
            }
        } else {
            // 其他页面(排除登录页面)显示打赏浮窗内容
            // 判断是否是onboarding页面，选择合适的标题
            const isOnboardingPage = currentUrl.includes('/account/onboarding');
            let title = '👋 感谢使用Augment Code助手';
            
            if (isOnboardingPage) {
                // 特别处理注册成功页面
                updateInfoPanel(`
                    <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #fff; text-align: center;">🎉 恭喜注册成功！</h3>
                    <p style="margin: 10px 0 15px 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.4;">感谢使用Augment Code助手<br>如果本工具对您有帮助，欢迎打赏支持作者~</p>

                    <!-- 联系作者按钮移到上方 -->
                    <button id="contact-author-onboarding" style="width: 100%; margin-bottom: 10px; background: linear-gradient(135deg, #07C160, #00A854) !important; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer; padding: 12px;">📱 联系作者</button>

                    <div id="wechat-qr-container-onboarding" style="display:none;margin-bottom:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                        <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                        <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                    </div>

                    <div style="margin-top: 15px; width: 100%;">
                        <!-- 并排显示的按钮组 -->
                        <div style="display:flex;gap:8px;margin-bottom:8px;">
                            <button id="download-reset-pack-onboarding" style="flex:1;padding:12px;background:linear-gradient(135deg, #FF6B6B, #FF8E53) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">📦 下载重置包</button>
                            <button id="go-to-profile" style="flex:1;padding:12px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">前往个人中心</button>
                        </div>
                        <!-- 垂直显示的其他按钮 -->
                        <button id="open-augmentcode" style="width: 100%; margin-bottom: 15px; background: linear-gradient(135deg, #11998e, #38ef7d) !important; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer; padding: 12px;">打开 Augment Code 使用账号</button>
                    </div>

                    <!-- 打赏相关内容移到下方 -->
                    <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 15px;">
                        <div style="display: flex; justify-content: space-between; width: 100%;">
                            <div class="tip-qrcode-container" style="position: relative; overflow: visible; text-align: center; width: 48%;">
                                <img src="https://www.liubao.org.cn/image/wx.png" alt="微信打赏" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                                <p style="margin: 5px 0 0; font-size: 12px; color: white;">微信打赏</p>
                            </div>
                            <div class="tip-qrcode-container" style="position: relative; overflow: visible; text-align: center; width: 48%;">
                                <img src="https://www.liubao.org.cn/image/zfb.png" alt="支付宝打赏" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                                <p style="margin: 5px 0 0; font-size: 12px; color: white;">支付宝打赏</p>
                            </div>
                        </div>
                        <p style="margin: 10px 0 0 0; font-size: 12px; color: #FFD700; text-align: center;">💡 扫码打赏并留言获取专属口令</p>
                    </div>
                `);


                
                // 添加悬停放大效果
                const style = document.createElement('style');
                style.id = 'tip-qrcode-style';
                style.textContent = `
                    .tip-qrcode-img:hover {
                        transform: scale(1.5);
                        z-index: 10000;
                    }
                `;
                if (!document.getElementById('tip-qrcode-style')) {
                    document.head.appendChild(style);
                }
                
                // 添加联系作者按钮功能
                document.getElementById('contact-author-onboarding')?.addEventListener('click', () => {
                    logDebug('点击onboarding页面联系作者按钮');
                    const qrContainer = document.getElementById('wechat-qr-container-onboarding');
                    const contactBtn = document.getElementById('contact-author-onboarding');

                    if (qrContainer && contactBtn) {
                        if (qrContainer.style.display === 'none') {
                            // 展开二维码
                            qrContainer.style.display = 'block';
                            contactBtn.textContent = '📱 收起联系';
                            contactBtn.style.background = 'linear-gradient(135deg, #FF6B6B, #FF5252) !important';
                        } else {
                            // 收起二维码
                            qrContainer.style.display = 'none';
                            contactBtn.textContent = '📱 联系作者';
                            contactBtn.style.background = 'linear-gradient(135deg, #07C160, #00A854) !important';
                        }
                    }
                });

                // 添加下载重置包按钮功能
                document.getElementById('download-reset-pack-onboarding')?.addEventListener('click', () => {
                    window.open('https://www.123684.com/s/sOUKVv-DAmxh', '_blank');
                });

                // 添加前往个人中心按钮功能
                document.getElementById('go-to-profile')?.addEventListener('click', () => {
                    window.location.href = 'https://app.augmentcode.com/account/subscription';
                });

                // 添加打开Augment Code按钮功能
                document.getElementById('open-augmentcode')?.addEventListener('click', () => {
                    window.open('https://app.augmentcode.com', '_blank');
                });
                
                return panel;
            }
            
            // 获取主题渐变色，用于按钮
            const themeGradient = 'linear-gradient(135deg, #4776E6, #8E54E9)';
            
            // 整合打赏面板内容到主面板
            updateInfoPanel(`
                <h3 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; color: #fff; text-align: center;">${title}</h3>
                <p style="margin: 10px 0 15px 0; color: #fff; font-size: 14px; text-align: center; line-height: 1.4;">如果本工具对您有帮助，欢迎打赏支持作者~</p>

                <!-- 联系作者按钮移到上方 -->
                <button id="contact-author-general" style="width: 100%; margin-bottom: 10px; background: linear-gradient(135deg, #07C160, #00A854) !important; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer; padding: 12px;">📱 联系作者</button>

                <div id="wechat-qr-container-general" style="display:none;margin-bottom:15px;text-align:center;border-top:1px solid rgba(255,255,255,0.2);padding-top:15px;">
                    <img src="https://liubao.org.cn/image/addwx.jpg" style="width:100%;max-width:200px;border-radius:8px;box-shadow:0 4px 8px rgba(0,0,0,0.3);margin:0 auto;display:block;">
                    <p style="margin:10px 0 0 0;color:#fff;font-size:12px;text-align:center;">🎯 扫码找作者，¥20 解锁成品号大礼包！</p>
                </div>

                <div style="margin-top: 15px; width: 100%;">
                    <!-- 并排显示的按钮组 -->
                    <div style="display:flex;gap:8px;margin-bottom:15px;">
                        <button id="download-reset-pack-general" style="flex:1;padding:12px;background:linear-gradient(135deg, #FF6B6B, #FF8E53) !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">📦 下载重置包</button>
                        <button id="go-to-profile" style="flex:1;padding:12px;background:${themeGradient} !important;color:white;border:none;border-radius:6px;font-size:14px;cursor:pointer;">前往个人中心</button>
                    </div>
                </div>

                <!-- 打赏相关内容移到下方 -->
                <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 15px;">
                    <div style="display: flex; justify-content: space-between; width: 100%;">
                        <div class="tip-qrcode-container" style="position: relative; overflow: visible; text-align: center; width: 48%;">
                            <img src="https://www.liubao.org.cn/image/wx.png" alt="微信打赏" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                            <p style="margin: 5px 0 0; font-size: 12px; color: white;">微信打赏</p>
                        </div>
                        <div class="tip-qrcode-container" style="position: relative; overflow: visible; text-align: center; width: 48%;">
                            <img src="https://www.liubao.org.cn/image/zfb.png" alt="支付宝打赏" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                            <p style="margin: 5px 0 0; font-size: 12px; color: white;">支付宝打赏</p>
                        </div>
                    </div>
                    <p style="margin: 10px 0 0 0; font-size: 12px; color: #FFD700; text-align: center;">💡 扫码打赏并留言获取专属口令</p>
                </div>
            `);


            
            // 添加悬停放大效果
            const style = document.createElement('style');
            style.id = 'tip-qrcode-style';
            style.textContent = `
                .tip-qrcode-img:hover {
                    transform: scale(1.5);
                    z-index: 10000;
                }
                #contact-author-general:hover {
                    transform: scale(1.05);
                    transition: transform 0.3s ease;
                }
                #contact-author-general {
                    transition: transform 0.3s ease;
                }
            `;
            if (!document.getElementById('tip-qrcode-style')) {
                document.head.appendChild(style);
            }
            
            // 添加联系作者按钮功能
            document.getElementById('contact-author-general')?.addEventListener('click', () => {
                logDebug('点击通用页面联系作者按钮');
                const qrContainer = document.getElementById('wechat-qr-container-general');
                const contactBtn = document.getElementById('contact-author-general');

                if (qrContainer && contactBtn) {
                    if (qrContainer.style.display === 'none') {
                        // 展开二维码
                        qrContainer.style.display = 'block';
                        contactBtn.textContent = '📱 收起联系';
                        contactBtn.style.background = 'linear-gradient(135deg, #FF6B6B, #FF5252) !important';
                    } else {
                        // 收起二维码
                        qrContainer.style.display = 'none';
                        contactBtn.textContent = '📱 联系作者';
                        contactBtn.style.background = 'linear-gradient(135deg, #07C160, #00A854) !important';
                    }
                }
            });

            // 添加下载重置包按钮功能
            document.getElementById('download-reset-pack-general')?.addEventListener('click', () => {
                window.open('https://www.123684.com/s/sOUKVv-DAmxh', '_blank');
            });

            // 添加前往个人中心按钮功能
            document.getElementById('go-to-profile')?.addEventListener('click', () => {
                window.location.href = 'https://app.augmentcode.com/account/subscription';
            });
        }

        return panel;
    }

    // 在页面加载完成后执行
    function initializeExtension() {
        logDebug('初始化扩展...');
        logDebug(`当前页面URL: ${window.location.href}`);

        // 重置状态
        verificationPageHandled = false;

        // 修改：移除域名限制，在所有页面都初始化扩展
        logDebug('开始初始化扩展（所有页面）');

        // 确保显示按钮存在
        let currentShowButton = document.getElementById('cursor-auto-show-button');
        if (!currentShowButton) {
            logDebug('显示按钮不存在，重新创建');
            currentShowButton = createShowButton();
        }

        // 显示浮窗按钮
        if (currentShowButton) {
            currentShowButton.style.display = 'block';
            logDebug('显示按钮已设置为可见');
        }

        // 检查是否在 Augment Code 相关页面，如果是则进行特殊处理
        if (window.location.href.includes('augmentcode.com')) {
            logDebug('检测到 Augment Code 页面，进行特殊处理');

            // 特殊处理推广页面 - 直接创建面板，不需要显示按钮
            if (window.location.href.includes('/promotions/')) {
                logDebug('检测到推广页面，直接创建面板');
                setTimeout(() => {
                    try {
                        createMainPanel();
                        logDebug('推广页面面板创建完成');
                        hasExecuted = true;
                    } catch (error) {
                        logDebug(`推广页面面板创建失败: ${error.message}`);
                        // 重试机制
                        setTimeout(() => {
                            try {
                                createMainPanel();
                                logDebug('推广页面面板重试创建完成');
                            } catch (retryError) {
                                logDebug(`推广页面面板重试创建失败: ${retryError.message}`);
                            }
                        }, 1000);
                    }
                }, 100);
                return;
            }

            // 对于 login.augmentcode.com 页面，直接创建主面板，不调用页面类型检查
            if (window.location.href.includes('login.augmentcode.com')) {
                logDebug('检测到 Augment Code 登录页面，直接创建主面板');
                setTimeout(() => {
                    try {
                        createMainPanel();
                        logDebug('Augment Code 登录页面主面板创建完成');
                        hasExecuted = true;
                    } catch (error) {
                        logDebug(`创建主面板失败: ${error.message}`);
                        // 重试一次
                        setTimeout(() => {
                            try {
                                createMainPanel();
                                logDebug('主面板重试创建完成');
                            } catch (retryError) {
                                logDebug(`主面板重试创建失败: ${retryError.message}`);
                            }
                        }, 1000);
                    }
                }, 100);
            } else {
                // 其他 Augment Code 页面的处理逻辑
                // 延迟创建主面板，确保DOM完全准备好
                setTimeout(() => {
                    try {
                        createMainPanel();
                        logDebug('主面板创建完成');
                        hasExecuted = true;
                    } catch (error) {
                        logDebug(`创建主面板失败: ${error.message}`);
                        // 重试一次
                        setTimeout(() => {
                            try {
                                createMainPanel();
                                logDebug('主面板重试创建完成');
                            } catch (retryError) {
                                logDebug(`主面板重试创建失败: ${retryError.message}`);
                            }
                        }, 1000);
                    }
                }, 100);

                // 同时调用页面类型检查，处理特殊页面逻辑
                setTimeout(() => {
                    checkPageTypeAndInitialize();
                }, 200);
            }
        } else {
            // 非 Augment Code 页面，显示通用面板
            logDebug('非 Augment Code 页面，显示通用面板');
            // 不自动创建面板，只显示按钮，让用户主动点击
        }
    }

    // 检查页面类型并初始化对应功能
    function checkPageTypeAndInitialize(forceCheck = false) {
        logDebug('检查页面类型并初始化对应功能');

        const currentUrl = window.location.href.toLowerCase();

        // 检查是否是Augment Code网站的页面
        if (currentUrl.includes('augmentcode.com')) {
            // 显示浮窗按钮，所有augmentcode页面都显示浮窗
            showButton.style.display = 'block';
            
            // 检查是否是登录页面，如果是登录页面，只显示打赏浮窗
            if (currentUrl.includes('/account/login')) {
                logDebug('检测到登录页面，只显示打赏浮窗');
                setTimeout(() => {
                    createMainPanel();
                }, 1000);
                return;
            }
            
            // 检查是否是验证码页面（扩大检测范围并增加优先级）
            const isVerificationPage = checkIfVerificationPage();

            // 检查是否是注册页面
            const isRegistrationPage = window.location.href.includes('/account/register');

            // 检查是否是密码设置页面
            const passwordInput = findElement(['input[type="password"]', 'input[name="password"]', 'input[placeholder*="password" i]', 'input[placeholder*="Create password" i]']);
            const passwordConfirmInput = findElement(['input[name="confirmPassword"]', 'input[id="passwordConfirmation"]', 'input[placeholder*="Confirm password" i]']);
            const isPasswordPage = passwordInput && passwordConfirmInput;

            logDebug(`页面类型检测结果: 验证码页=${isVerificationPage}, 注册页=${isRegistrationPage}, 密码页=${isPasswordPage}, 强制检查=${forceCheck}`);

            // 如果是验证码页面，先尝试从页面获取邮箱地址
            if (isVerificationPage && (forceCheck || !verificationPageHandled)) {
                logDebug('检测到验证码页面，开始处理验证码流程');

                // 标记已处理，避免重复处理
                verificationPageHandled = true;

                // 从页面提取邮箱并更新到账号信息中
                updateEmailFromPage();

                // 创建主面板
                createMainPanel();

                // 自动触发验证码获取流程
                setTimeout(() => {
                    triggerGetVerificationCode(forceCheck);
                }, 500);

                return;
            }

            // 如果是注册页面或密码页面，初始化对应功能
            if ((isRegistrationPage || isPasswordPage) && !verificationPageHandled) {
                // 创建主面板
                createMainPanel();


            } else {
                // 其他页面 - 创建统一的主面板（包含打赏功能）
                setTimeout(() => {
                    createMainPanel();
                }, 1000);
            }
        }
    }

    // 检查是否为验证码页面（更详细的检查）
    function checkIfVerificationPage() {
        // 首先判断是否是登录页面、onboarding页面、协议页面或Augment Code登录页面，如果是则直接返回false
        if (window.location.href.toLowerCase().includes('/account/login') ||
            window.location.href.toLowerCase().includes('/account/onboarding') ||
            window.location.href.toLowerCase().includes('login.augmentcode.com') ||
            window.location.href.toLowerCase().includes('auth.augmentcode.com/terms-accept')) {
            logDebug('当前是登录页面、注册成功页面、协议页面或Augment Code登录页面，不应识别为验证码页面');
            return false;
        }
        
        // 输出当前页面URL和标题，便于调试
        logDebug(`检查页面类型，当前URL: ${window.location.href}`);
        logDebug(`页面标题: ${document.title}`);
        logDebug(`页面文本前100字符: ${document.body.innerText.substring(0, 100)}...`);

        // 优先检查页面文本中是否包含验证码关键词
        const bodyText = document.body.innerText || "";
        const pageSource = document.documentElement.outerHTML || "";

        // 特别检查是否包含"To create an account, enter the code sent to"文本 - 这个是最高优先级
        if (bodyText.includes("To create an account, enter the code sent to")) {
            logDebug('已检测到关键文本"To create an account, enter the code sent to"，确认为验证码页面');
            return true;
        }
        
        // 检查是否包含其他验证码关键文本
        const verificationTexts = [
            "Enter the code sent to",
            "Verify your email",
            "Email verification",
            "Enter verification code",
            "Enter code sent to",
            "Verification code",
            "Email confirmation code",
            "Code sent to",
            "Please enter the code"
        ];
        
        for (const text of verificationTexts) {
            if (bodyText.includes(text)) {
                logDebug(`检测到验证码关键文本: "${text}"，确认为验证码页面`);
                return true;
            }
        }

        // 1. 检查URL
        if (window.location.href.includes('email-verification') ||
            window.location.href.includes('verify') ||
            window.location.href.includes('otp') ||
            window.location.href.includes('confirmation')) {
            logDebug('通过URL检测到验证码页面');
            return true;
        }

        // 2. 检查是否有多个单字符输入框 (验证码输入框特征)
        const separateInputs = document.querySelectorAll('input[maxlength="1"]');
        if (separateInputs.length >= 4) {
            logDebug(`检测到${separateInputs.length}个验证码输入框`);
            return true;
        }
        
        // 检查是否有特定的验证码输入框组合
        const codeInputs = document.querySelectorAll('.verification-code-input, .code-input, [data-testid="code-input"]');
        if (codeInputs.length > 0) {
            logDebug(`检测到${codeInputs.length}个验证码输入框容器`);
            return true;
        }

        // 4. 检查是否有隐藏的OTP输入字段
        const otpInput = document.querySelector('input[name="otpCode"], input[name="otp"], input[name="code"], input[name="verificationCode"]');
        if (otpInput) {
            logDebug(`检测到验证码输入字段: ${otpInput.name}`);
            return true;
        }

        // 5. 通过页面文本深度检测
        const lowerText = bodyText.toLowerCase();

        // 更多验证码相关关键词
        const verificationKeywords = [
            'code sent to',
            'verification code',
            'enter the code',
            'confirm your email',
            'email verification',
            'email confirmation',
            'verify your email',
            'verify your account',
            '验证码',
            '验证邮箱',
            'validation code',
            'security code'
        ];

        for (const text of verificationKeywords) {
            if (lowerText.includes(text)) {
                logDebug(`通过文本关键词"${text}"检测到验证码页面`);
                return true;
            }
        }

        // 通过页面特征组合判断
        if ((lowerText.includes('code') || lowerText.includes('验证码') || lowerText.includes('verification')) &&
            (lowerText.includes('email') || lowerText.includes('邮箱') || lowerText.includes('sent to'))) {
            logDebug('通过页面文本组合特征检测到验证码页面');
            return true;
        }

        logDebug('当前页面不是验证码页面');
        return false;
    }

    // 从页面提取邮箱并更新到账号信息
    function updateEmailFromPage() {
        const emailFromPage = extractEmailFromPage();
        if (emailFromPage) {
            logDebug(`从页面提取到邮箱: ${emailFromPage}`);

            // 更新账号信息中的邮箱
            chrome.runtime.sendMessage({ type: "getAccountInfo" }, (response) => {
                const accountInfo = response?.accountInfo || {};
                if (accountInfo.email !== emailFromPage) {
                    logDebug(`页面邮箱(${emailFromPage})与存储邮箱(${accountInfo.email || '无'})不一致，更新账号信息`);
                    chrome.runtime.sendMessage({
                        type: "setAccountInfo",
                        accountInfo: { ...accountInfo, email: emailFromPage }
                    });
                }
            });

            return emailFromPage;
        }
        return null;
    }

    // 创建DOM变化监听器
    function createDomChangeObserver() {
        // 如果已经有观察器，先断开连接
        if (verificationPageObserver) {
            verificationPageObserver.disconnect();
            logDebug('断开现有DOM观察器连接');
        }

        // 创建新的观察器
        logDebug('创建新的DOM变化观察器');

        // 防抖定时器
        let debounceTimer = null;

        // 上次处理时间
        let lastProcessTime = 0;
        const throttleDelay = 1000; // 1秒内不重复处理

        verificationPageObserver = new MutationObserver((mutations) => {
            // 检查是否有与验证码相关的变化
            let hasCodeRelatedChanges = false;
            let hasTextChanges = false;
            let hasRelevantAttributeChanges = false;

            // 密码填写后的特殊时间窗口，提高敏感度
            const isInPostPasswordTimeWindow = Date.now() - passwordFilledTimestamp < 30000; // 30秒内

            // 检查是否是在注册URL下
            const isRegisterPage = window.location.href.includes('/account/register');
            
            // 如果在注册页面且在密码填写后的时间窗口内，更积极地检测验证码页面
            if (isRegisterPage && isInPostPasswordTimeWindow) {
                // 立即检查是否是验证码页面
                if (checkIfVerificationPage()) {
                    logDebug('密码填写后检测到验证码页面，立即更新浮窗');
                    // 更新浮窗为验证码填充界面
                    createMainPanel();
                    // 自动触发验证码获取
                    setTimeout(() => {
                        triggerGetVerificationCode(true);
                    }, 500);
                    return;
                }
            }

            for (const mutation of mutations) {
                // 检查文本节点变化
                if (mutation.type === 'characterData') {
                    hasTextChanges = true;
                    break;
                }

                // 检查属性变化
                if (mutation.type === 'attributes' &&
                    (mutation.target.tagName === 'INPUT' ||
                     mutation.target.tagName === 'FORM' ||
                     mutation.target.tagName === 'BUTTON')) {
                    hasRelevantAttributeChanges = true;
                }

                // 检查节点添加
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (const node of mutation.addedNodes) {
                        // 检查添加的元素节点
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 输入框、表单元素和文本变化都是关注点
                            if (node.tagName === 'INPUT' ||
                                node.tagName === 'FORM' ||
                                node.querySelector('input') ||
                                node.querySelector('form')) {
                                hasCodeRelatedChanges = true;
                                break;
                            }

                            // 特别注意文本变化
                            if (node.innerText && (node.innerText.includes('code') ||
                                node.innerText.includes('验证码') ||
                                node.innerText.includes('verification') ||
                                node.innerText.includes('enter') ||
                                node.innerText.includes('To create an account') ||
                                node.innerText.includes('Switching back to'))) {
                                hasTextChanges = true;
                                break;
                            }
                            
                            // 增加对验证码页面的检测 - 检查特定的类名和结构
                            if (node.classList && 
                                (node.classList.contains('verification-code') || 
                                 node.classList.contains('code-input') || 
                                 node.getAttribute('data-testid') === 'code-input')) {
                                logDebug('检测到验证码相关的DOM元素添加');
                                hasCodeRelatedChanges = true;
                                break;
                            }
                            
                            // 检查子元素是否有验证码相关元素
                            const codeInputs = node.querySelectorAll('input[maxlength="1"]');
                            if (codeInputs.length >= 4) {
                                logDebug(`检测到验证码输入框添加: ${codeInputs.length}个`);
                                hasCodeRelatedChanges = true;
                                break;
                            }
                        }
                        // 也检查文本节点
                        else if (node.nodeType === Node.TEXT_NODE) {
                            if (node.textContent && (node.textContent.includes('code') ||
                                node.textContent.includes('验证码') ||
                                node.textContent.includes('To create an account') ||
                                node.textContent.includes('Switching back to'))) {
                                hasTextChanges = true;
                                break;
                            }
                        }
                    }
                }

                if (hasCodeRelatedChanges || hasTextChanges) break;
            }

            // 如果在密码填写后的时间窗口内，任何变化都当作重要变化
            if (isInPostPasswordTimeWindow && (mutations.length > 0)) {
                logDebug('密码填写后时间窗口内检测到DOM变化，视为重要变化');
                hasCodeRelatedChanges = true;
            }

            // 应用节流，避免过于频繁地触发检查
            const now = Date.now();
            if (now - lastProcessTime < throttleDelay && !isInPostPasswordTimeWindow) {
                return; // 太频繁，忽略这次变化
            }

            // 任何文本变化都重新检查页面类型
            if (hasTextChanges || hasCodeRelatedChanges || hasRelevantAttributeChanges) {
                // 清除之前的定时器
                if (debounceTimer) {
                    clearTimeout(debounceTimer);
                }

                // 设置防抖定时器
                debounceTimer = setTimeout(() => {
                    logDebug('检测到DOM重要变化，重新检查页面类型');
                    lastProcessTime = Date.now(); // 更新最后处理时间

                    // 优先检查是否出现注册成功标识
                    const currentPageText = document.body.innerText || '';
                    if (currentPageText.includes('Switching back to')) {
                        logDebug('DOM变化检测到注册成功标识，更新为注册成功页面');
                        createMainPanel();
                        return;
                    }

                    // 优先检查是否是登录页面
                    if (window.location.href.toLowerCase().includes('/account/login')) {
                        logDebug('DOM变化后检测到登录页面，不进行验证码处理');
                        return;
                    }

                    const isVerificationPage = checkIfVerificationPage();
                    if (isVerificationPage) {
                        logDebug('DOM变化后检测到验证码页面，触发处理流程');
                        // 更新浮窗显示验证码获取界面
                        createMainPanel();
                        
                        // 如果在密码填写后的时间窗口内，自动触发验证码获取
                        if (isInPostPasswordTimeWindow && !verificationCodeFetching) {
                            setTimeout(() => {
                                triggerGetVerificationCode(true);
                            }, 500);
                        }
                    } else if (!isVerificationPage) {
                        // 如果不是验证码页面，重置状态
                        verificationPageHandled = false;
                    }
                }, isInPostPasswordTimeWindow ? 200 : 500); // 密码填写后时间窗口内更快响应
            }
        });

        // 开始观察整个文档，增加配置选项提高敏感度
        verificationPageObserver.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: true, // 监听文本变化
            attributeFilter: ['class', 'style', 'id', 'value'] // 特别关注这些属性变化
        });

        logDebug('DOM变化监听器已创建并开始监听');
    }

    // 自动触发获取验证码功能 - 增强版
    async function triggerGetVerificationCode(force = false) {
        logDebug(`尝试触发验证码获取流程 (强制=${force})`);

        // 如果已经在获取中且不是强制模式，则返回
        if (verificationCodeFetching && !force) {
            logDebug('验证码获取已在进行中，非强制模式下不重复触发');
            return;
        }

        // 避免多次同时触发
        if (verificationCodeFetching) {
            logDebug('验证码获取已在进行中，但为强制模式，允许重试');
        }

        // 无论如何设置状态为正在获取
        verificationCodeFetching = true;

        try {
            logDebug('开始获取验证码流程');

            // 更新面板显示正在获取验证码
            updateInfoPanel(`
                <h3>正在获取验证码</h3>
                <p>正在从邮箱中获取验证码，请稍候...</p>
                <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
            `);

            // 获取邮箱地址的流程
            const emailFromPage = extractEmailFromPage();
            let emailToUse = null;

            // 输出当前找到的邮箱
            logDebug(`从页面提取到的邮箱: ${emailFromPage || '无'}`);

            if (emailFromPage) {
                logDebug(`使用从页面提取的邮箱: ${emailFromPage}`);
                emailToUse = emailFromPage;

                // 更新存储的账号信息
                chrome.runtime.sendMessage({
                    type: "getAccountInfo"
                }, (response) => {
                    const accountInfo = response?.accountInfo || {};
                    if (accountInfo.email !== emailFromPage) {
                        chrome.runtime.sendMessage({
                            type: "setAccountInfo",
                            accountInfo: { ...accountInfo, email: emailFromPage }
                        });
                        logDebug(`已更新存储账号信息中的邮箱`);
                    }
                });
            } else {
                // 从存储中获取账号信息
                const response = await new Promise(resolve => {
                    chrome.runtime.sendMessage({ type: "getAccountInfo" }, (response) => {
                        resolve(response);
                    });
                });

                const accountInfo = response?.accountInfo || {};
                emailToUse = accountInfo.email;
                logDebug(`从存储账号信息中获取邮箱: ${emailToUse || '无'}`);
            }

            if (!emailToUse) {
                // 如果仍未找到邮箱，尝试通过提示用户输入
                logDebug('未能自动获取邮箱地址，提示用户手动输入');
                updateInfoPanel(`
                    <h3>获取验证码</h3>
                    <p>未能自动获取邮箱地址，请手动输入：</p>
                    <div style="margin-bottom:10px;">
                        <input type="email" id="manual-email" placeholder="请输入注册邮箱" style="width:100%;padding:8px;border-radius:4px;border:1px solid #ccc;margin-top:5px;background:#333;color:white;">
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                        <button id="use-manual-email" style="width:100%;margin-bottom:5px;">获取验证码</button>
                        <button id="back-to-main" style="width:100%;">返回主菜单</button>
                    </div>
                `);

                document.getElementById('back-to-main')?.addEventListener('click', () => {
                    createMainPanel();
                });

                document.getElementById('use-manual-email')?.addEventListener('click', () => {
                    const manualEmail = document.getElementById('manual-email')?.value;
                    if (manualEmail && manualEmail.includes('@')) {
                        // 保存邮箱并重试获取验证码
                        chrome.runtime.sendMessage({
                            type: "getAccountInfo"
                        }, (response) => {
                            const accountInfo = response?.accountInfo || {};
                            chrome.runtime.sendMessage({
                                type: "setAccountInfo",
                                accountInfo: { ...accountInfo, email: manualEmail }
                            }, () => {
                                verificationCodeFetching = false;
                                triggerGetVerificationCode(true);
                            });
                        });
                    } else {
                        showNotification('请输入有效的邮箱地址', 'error');
                    }
                });

                verificationCodeFetching = false;
                return;
            }

            // 显示正在使用的邮箱地址
            updateInfoPanel(`
                <h3>正在获取验证码</h3>
                <p>使用邮箱: <strong>${emailToUse}</strong></p>
                <p>正在从邮箱中获取验证码，请稍候...</p>
                <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
            `);

            // 获取验证码 - 增加重试机制
            logDebug(`尝试从邮箱 ${emailToUse} 获取验证码`);
            let code = null;
            let attempts = 0;
            const maxAttempts = 3;
            const waitBetweenAttempts = 8000; // 8秒

            while (!code && attempts < maxAttempts) {
                attempts++;
                try {
                    logDebug(`第 ${attempts}/${maxAttempts} 次尝试获取验证码`);

                    // 更新面板显示当前尝试次数
                    updateInfoPanel(`
                        <h3>正在获取验证码</h3>
                        <p>使用邮箱: <strong>${emailToUse}</strong></p>
                        <p>正在尝试获取验证码 (${attempts}/${maxAttempts})...</p>
                        <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                        <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
                    `);

                    code = await getVerificationCode(emailToUse);
                    if (code) {
                        logDebug(`成功获取到验证码: ${code}`);
                        break;
                    }

                    // 如果没获取到验证码且未达到最大尝试次数，等待并重试
                    if (attempts < maxAttempts) {
                        logDebug(`第${attempts}次获取验证码失败，等待${waitBetweenAttempts/1000}秒后重试...`);

                        // 显示等待倒计时
                        const startTime = Date.now();
                        const endTime = startTime + waitBetweenAttempts;

                        // 使用条件变量控制循环，而不是使用break跳出
                        let shouldContinueWaiting = true;
                        let userCanceled = false;

                        while (Date.now() < endTime && shouldContinueWaiting) {
                            const remainingTime = Math.ceil((endTime - Date.now()) / 1000);
                            updateInfoPanel(`
                                <h3>正在等待重试</h3>
                                <p>使用邮箱: <strong>${emailToUse}</strong></p>
                                <p>第${attempts}次获取验证码未成功，将在 <strong>${remainingTime}</strong> 秒后重试...</p>
                                <div class="progress" style="width:100%;height:6px;background:#444;border-radius:3px;margin:15px 0;">
                                    <div class="progress-bar" style="width:${100 - (remainingTime * 1000 / waitBetweenAttempts * 100)}%;height:100%;background:#4CAF50;border-radius:3px;"></div>
                                </div>
                                <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:15px;">
                                    <button id="retry-now" style="width:100%;">立即重试</button>
                                    <button id="cancel-retry" style="width:100%;margin-top:5px;">取消获取</button>
                                </div>
                            `);

                            // 添加立即重试和取消按钮功能
                            document.getElementById('retry-now')?.addEventListener('click', () => {
                                logDebug('用户点击立即重试');
                                shouldContinueWaiting = false; // 停止等待
                            });

                            document.getElementById('cancel-retry')?.addEventListener('click', () => {
                                logDebug('用户取消验证码获取');
                                shouldContinueWaiting = false; // 停止等待
                                userCanceled = true; // 标记取消状态
                            });

                            // 更新倒计时显示
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }

                        // 处理用户取消的情况
                        if (userCanceled) {
                            createMainPanel();
                            verificationCodeFetching = false;
                            return;
                        }
                    }
                } catch (error) {
                    logDebug(`第${attempts}次获取验证码出错: ${error.message}`);
                    if (error.message === '用户取消验证码获取') {
                        createMainPanel();
                        verificationCodeFetching = false;
                        return;
                    }

                    if (attempts < maxAttempts) {
                        await new Promise(resolve => setTimeout(resolve, 5000));
                    }
                }
            }

            if (code) {
                logDebug(`成功获取验证码: ${code}`);

                // 更新面板显示验证码
                updateInfoPanel(`
                    <h3>验证码获取成功</h3>
                    <p>为邮箱 ${emailToUse} 获取的验证码:</p>
                    <p style="font-size:24px;font-weight:bold;text-align:center;margin:10px 0;letter-spacing:5px;color:#4CAF50;">${code}</p>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                        <button id="copy-code" style="width:100%;margin-bottom:5px;background:#4CAF50 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">📋 复制验证码</button>
                        <button id="retry-get-code" style="width:100%;margin-bottom:5px;background:#FF9800 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">🔄 重新获取验证码</button>
                        <button id="back-to-main" style="width:100%;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">返回主菜单</button>
                    </div>
                `);

                document.getElementById('copy-code')?.addEventListener('click', () => {
                    navigator.clipboard.writeText(code).then(() => {
                        showNotification('验证码已复制');
                    });
                });

                document.getElementById('retry-get-code')?.addEventListener('click', () => {
                    verificationCodeFetching = false; // 重置状态以允许重试
                    triggerGetVerificationCode(true); // 使用强制模式重新获取
                });

                document.getElementById('back-to-main')?.addEventListener('click', () => {
                    createMainPanel();
                });


            } else {
                // 获取验证码失败，提供手动输入选项
                logDebug('多次尝试后仍未能获取验证码，提供手动输入选项');
                updateInfoPanel(`
                    <h3>获取验证码失败</h3>
                    <p>多次尝试后仍未能从邮箱 ${emailToUse} 中获取验证码。</p>
                    <p>可能原因:</p>
                    <ul style="margin-left:20px;padding-left:0;">
                        <li>邮件尚未送达，稍后再试</li>
                        <li>邮箱API暂时不可用</li>
                        <li>验证码格式与预期不符</li>
                    </ul>
                    <p style="margin-top:10px;">您可以选择:</p>
                    <div style="margin-top:10px;">
                        <input type="text" id="manual-code" placeholder="请手动输入验证码" style="width:100%;padding:8px;border-radius:4px;border:1px solid #ccc;background:#333;color:white;">
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                        <button id="use-manual-code" style="width:48%;background:#4CAF50 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">使用此验证码</button>
                        <button id="retry-code" style="width:48%;background:#FF9800 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">重试获取</button>
                        <button id="back-to-main" style="width:100%;margin-top:5px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">返回主菜单</button>
                    </div>
                `);

                // 绑定手动输入验证码按钮
                document.getElementById('use-manual-code')?.addEventListener('click', () => {
                    const manualCode = document.getElementById('manual-code')?.value;
                    if (manualCode && /^\d{6}$/.test(manualCode)) {
                        navigator.clipboard.writeText(manualCode).then(() => {
                            showNotification('验证码已复制');
                        });
                    } else {
                        showNotification('请输入有效的6位验证码', 'error');
                    }
                });

                document.getElementById('retry-code')?.addEventListener('click', () => {
                    verificationCodeFetching = false; // 重置状态以允许重试
                    triggerGetVerificationCode(true); // 使用强制模式
                });

                document.getElementById('back-to-main')?.addEventListener('click', () => {
                    createMainPanel();
                });
            }
        } catch (error) {
            logDebug(`获取验证码过程出错: ${error.message}`);

            // 显示错误信息
            updateInfoPanel(`
                <h3>获取验证码出错</h3>
                <p>错误信息: ${error.message}</p>
                <div style="margin-top:10px;">
                    <input type="text" id="manual-code" placeholder="请手动输入验证码" style="width:100%;padding:8px;border-radius:4px;border:1px solid #ccc;background:#333;color:white;">
                </div>
                <div style="display:flex;flex-wrap:wrap;gap:5px;margin-top:10px;">
                    <button id="use-manual-code" style="width:48%;background:#4CAF50 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">使用此验证码</button>
                    <button id="retry-code" style="width:48%;background:#FF9800 !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">重试获取</button>
                    <button id="back-to-main" style="width:100%;margin-top:5px;background:linear-gradient(135deg, #4776E6, #8E54E9) !important;color:white;border:none;border-radius:4px;padding:10px;font-size:14px;cursor:pointer;">返回主菜单</button>
                </div>
            `);

            document.getElementById('use-manual-code')?.addEventListener('click', () => {
                const manualCode = document.getElementById('manual-code')?.value;
                if (manualCode && /^\d{6}$/.test(manualCode)) {
                    navigator.clipboard.writeText(manualCode).then(() => {
                        showNotification('验证码已复制');
                    });
                } else {
                    showNotification('请输入有效的6位验证码', 'error');
                }
            });

            document.getElementById('retry-code')?.addEventListener('click', () => {
                verificationCodeFetching = false; // 重置状态以允许重试
                triggerGetVerificationCode(true); // 使用强制模式
            });

            document.getElementById('back-to-main')?.addEventListener('click', () => {
                createMainPanel();
            });
        } finally {
            verificationCodeFetching = false;
        }
    }

    // 从页面提取邮箱地址 - 增强版
    function extractEmailFromPage() {
        try {
            logDebug('尝试从页面提取邮箱地址');

            // 特别检查"To create an account, enter the code sent to"后面的邮箱
            const bodyText = document.body.innerText || "";
            const specialCodePattern = /To create an account, enter the code sent to\s+([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i;
            const specialMatch = bodyText.match(specialCodePattern);
            if (specialMatch && specialMatch[1]) {
                logDebug(`从"To create an account, enter the code sent to"后找到邮箱: ${specialMatch[1]}`);
                return specialMatch[1];
            }

            // 方法1: 直接从URL中提取
            const urlParams = new URLSearchParams(window.location.search);
            const emailParam = urlParams.get('email') || urlParams.get('mail') || urlParams.get('address');
            if (emailParam && emailParam.includes('@')) {
                logDebug(`从URL参数提取到邮箱: ${emailParam}`);
                return emailParam;
            }

            // 方法2: 从页面文本中提取邮箱
            const emailRegexes = [
                /enter the code sent to\s+([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i,
                /sent to\s+([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i,
                /发送到\s+([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i,
                /email\s+([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i,
                /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i
            ];

            for (const regex of emailRegexes) {
                const match = bodyText.match(regex);
                if (match && match[1]) {
                    logDebug(`通过正则表达式从页面文本提取到邮箱: ${match[1]}`);
                    return match[1];
                }
            }

            // 方法3: 查找所有font-medium类的span和其他可能包含邮箱的元素
            const potentialElements = [
                ...document.querySelectorAll('span.font-medium'),
                ...document.querySelectorAll('span.email'),
                ...document.querySelectorAll('[class*="email"]'),
                ...document.querySelectorAll('strong'),
                ...document.querySelectorAll('b'),
                ...document.querySelectorAll('span'),
                ...document.querySelectorAll('p')
            ];

            const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
            for (const element of potentialElements) {
                if (!element || !element.textContent) continue;

                const text = element.textContent.trim();
                const match = text.match(emailRegex);
                if (match) {
                    logDebug(`从元素 ${element.tagName}${element.className ? '.' + element.className : ''} 提取到邮箱: ${match[0]}`);
                    return match[0];
                }
            }

            // 方法4: 深度扫描所有文本节点
            const textNodes = [];
            const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT);
            let node;
            while (node = walker.nextNode()) {
                if (!node.textContent) continue;

                const text = node.textContent.trim();
                if (text.length > 5) { // 忽略太短的文本
                    textNodes.push(node);
                }
            }

            for (const node of textNodes) {
                const text = node.textContent.trim();
                const emailMatches = text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g);
                if (emailMatches && emailMatches.length > 0) {
                    logDebug(`从文本节点提取到邮箱: ${emailMatches[0]}`);
                    return emailMatches[0];
                }
            }

            // 方法5: 查找可能包含邮箱的隐藏输入字段
            const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
            for (const input of hiddenInputs) {
                if (input.value && input.value.includes('@')) {
                    const match = input.value.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
                    if (match) {
                        logDebug(`从隐藏输入字段提取到邮箱: ${match[0]}`);
                        return match[0];
                    }
                }
            }

            logDebug('未能从页面提取到邮箱地址');
            return null;
        } catch (e) {
            logDebug(`提取邮箱过程出错: ${e.message}`);
            return null;
        }
    }

    // 创建全新账号（不复用已有账号）
    async function createNewAccount() {
        try {
            // 更新UI显示创建进度
            updateInfoPanel(`
                <h3>正在创建新账号</h3>
                <p>正在生成随机信息...</p>
                <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
            `);

            // 首先获取配置信息
            const config = await getConfig();
            // 更新默认配置，确保使用最新的用户设置
            if (config) {
                // 只更新存在的配置项
                Object.assign(defaultConfig, config);
            }

            // 生成随机信息（确保姓名不含数字）
            const firstName = generateRandomName();
            const lastName = generateRandomName();
            const password = generateRandomPassword();

            // 生成随机邮箱用户名
            const randomEmailName = generateRandomString(10);

            // 更新UI显示创建进度
            updateInfoPanel(`
                <h3>正在创建新账号</h3>
                <p>正在创建临时邮箱...</p>
                <div class="loader" style="border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:20px;height:20px;animation:spin 2s linear infinite;margin:10px auto;"></div>
                <style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>
                <p><small>名: ${firstName}<br>姓: ${lastName}</small></p>
            `);

            try {
                // 调用后台API创建真实邮箱
                logDebug(`请求创建临时邮箱: ${randomEmailName}`);
                const emailResponse = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({
                        type: "createEmail",
                        apiUrl: defaultConfig.tempEmailApi,
                        emailName: randomEmailName
                    }, (response) => {
                        if (response && response.success) {
                            resolve(response);
                        } else {
                            reject(new Error(response?.error || "创建邮箱失败"));
                        }
                    });
                });

                logDebug(`邮箱创建成功: ${JSON.stringify(emailResponse)}`);

                // 使用API返回的邮箱地址和密码
                const email = emailResponse.email;
                const emailPassword = emailResponse.password || password; // 如果API未返回密码则使用生成的随机密码

                const accountInfo = {
                    firstName: firstName,
                    lastName: lastName,
                    email: email,
                    password: emailPassword
                };

                // 更新UI显示创建完成
                updateInfoPanel(`
                    <h3>账号创建成功</h3>
                    <div style="margin-bottom:10px;">
                        <p><strong>名:</strong> ${firstName}</p>
                        <p><strong>姓:</strong> ${lastName}</p>
                        <p><strong>邮箱:</strong> ${email}</p>
                        <p><strong>密码:</strong> ${emailPassword}</p>
                    </div>
                    <div style="display:flex;justify-content:space-between;margin-bottom:10px;">
                        <button id="copy-new-email" style="background:#4CAF50;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:12px;width:48%;">复制邮箱</button>
                        <button id="copy-new-password" style="background:#4CAF50;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:12px;width:48%;">复制密码</button>
                    </div>
                    <div style="display:flex;justify-content:center;margin-bottom:10px;">
                        <button id="back-to-main-new" style="background:#607D8B;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;width:100%;">返回主菜单</button>
                    </div>
                `);

                // 添加通用联系作者按钮
                addContactAuthorButton();

                // 添加复制按钮功能
                document.getElementById('copy-new-email')?.addEventListener('click', () => {
                    navigator.clipboard.writeText(email).then(() => {
                        showNotification('邮箱已复制');
                    }).catch(err => {
                        showNotification('复制失败: ' + err.message, 'error');
                    });
                });

                document.getElementById('copy-new-password')?.addEventListener('click', () => {
                    navigator.clipboard.writeText(emailPassword).then(() => {
                        showNotification('密码已复制');
                    }).catch(err => {
                        showNotification('复制失败: ' + err.message, 'error');
                    });
                });



                document.getElementById('back-to-main-new')?.addEventListener('click', () => {
                    createMainPanel();
                    showNotification('已返回主菜单');
                });

                // 存储账号信息以便后续使用
                chrome.runtime.sendMessage({ 
                    type: "saveAccountInfo", 
                    email: accountInfo.email,
                    password: accountInfo.password 
                }, () => {
                    logDebug(`生成新账号信息并存储: ${JSON.stringify(accountInfo)}`);
                });

                return accountInfo;
            } catch (error) {
                // 创建邮箱失败时的处理
                logDebug(`邮箱创建失败: ${error.message}`);
                showNotification(`邮箱创建失败: ${error.message}`, 'error');

                // 使用本地生成的邮箱作为备选，使用随机域名
                const fallbackEmail = `${randomEmailName}@${getRandomEmailDomain()}`;

                const accountInfo = {
                    firstName: firstName,
                    lastName: lastName,
                    email: fallbackEmail,
                    password: password
                };

                // 更新UI显示创建失败，但提供备选方案
                updateInfoPanel(`
                    <h3>邮箱创建出错</h3>
                    <p>出错信息: ${error.message}</p>
                    <div style="margin-bottom:10px;">
                    <p>已创建本地备选方案:</p>
                        <p><strong>名:</strong> ${firstName}</p>
                        <p><strong>姓:</strong> ${lastName}</p>
                        <p><strong>邮箱:</strong> ${fallbackEmail}</p>
                        <p><strong>密码:</strong> ${password}</p>
                    </div>
                    <div style="display:flex;justify-content:space-between;margin-bottom:10px;">
                        <button id="copy-fallback-email" style="background:#4CAF50;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:12px;width:48%;">复制邮箱</button>
                        <button id="copy-fallback-password" style="background:#4CAF50;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:12px;width:48%;">复制密码</button>
                    </div>
                    <div style="display:flex;justify-content:space-between;margin-bottom:10px;">
                        <button id="retry-email" style="background:#FF9800;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;width:100%;">重试创建</button>
                    </div>
                    <button id="back-to-main-fallback" style="background:#607D8B;border:none;color:white;padding:5px 10px;border-radius:3px;cursor:pointer;width:100%;">返回主菜单</button>
                `);



                // 添加复制按钮功能
                document.getElementById('copy-fallback-email')?.addEventListener('click', () => {
                    navigator.clipboard.writeText(fallbackEmail).then(() => {
                        showNotification('备选邮箱已复制');
                    }).catch(err => {
                        showNotification('复制失败: ' + err.message, 'error');
                    });
                });

                document.getElementById('copy-fallback-password')?.addEventListener('click', () => {
                    navigator.clipboard.writeText(password).then(() => {
                        showNotification('备选密码已复制');
                    }).catch(err => {
                        showNotification('复制失败: ' + err.message, 'error');
                    });
                });



                document.getElementById('back-to-main-fallback')?.addEventListener('click', () => {
                    createMainPanel();
                    showNotification('已返回主菜单');
                });

                document.getElementById('retry-email')?.addEventListener('click', () => {
                    // 重新尝试创建账号
                    createNewAccount().then(info => {
                        autoFillSignupForm(info);
                    });
                });

                // 存储备选账号信息
                chrome.runtime.sendMessage({ 
                    type: "saveAccountInfo", 
                    email: accountInfo.email,
                    password: accountInfo.password 
                }, () => {
                    logDebug(`生成备选账号信息并存储: ${JSON.stringify(accountInfo)}`);
                });

                return accountInfo;
            }
        } catch (error) {
            logDebug(`创建账号出错: ${error.message}`);
            showNotification(`创建账号出错: ${error.message}`, 'error');
            return null;
        }
    }









    // 填充Augment Code验证码
    async function fillAugmentCodeVerificationCode(code) {
        logDebug(`开始填充Augment Code验证码: ${code}`);

        try {
            // 查找验证码输入框
            const codeInput = findElement([
                'input[name="code"]',
                'input[id="code"]',
                'input[type="text"][autocomplete="off"]',
                'input[placeholder*="code" i]',
                'input[placeholder*="验证码"]'
            ]);

            if (codeInput) {
                logDebug('找到验证码输入框，开始填充');

                // 填充验证码
                const success = await setInputValue(codeInput, code);

                if (success) {
                    logDebug('验证码填充成功');
                    showNotification(`验证码已填充: ${code}`);

                    // 等待一下然后查找并点击提交按钮
                    setTimeout(() => {
                        const submitButton = findElement([
                            'button[type="submit"]',
                            'button:contains("Verify")',
                            'button:contains("Submit")',
                            'button:contains("Continue")',
                            'button:contains("验证")',
                            'button:contains("提交")',
                            'form button'
                        ]);

                        if (submitButton) {
                            logDebug('找到提交按钮，准备点击');
                            submitButton.click();

                            // 如果直接点击失败，尝试触发事件
                            setTimeout(() => {
                                submitButton.dispatchEvent(new MouseEvent('click', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                }));
                            }, 100);

                            logDebug('已点击提交按钮');
                            showNotification('验证码已提交');
                        } else {
                            logDebug('未找到提交按钮');
                            showNotification('验证码已填充，请手动点击提交按钮');
                        }
                    }, 1000);
                } else {
                    logDebug('验证码填充失败');
                    showNotification('验证码填充失败，请手动输入', 'error');
                }
            } else {
                logDebug('未找到验证码输入框');
                showNotification('未找到验证码输入框，请手动输入: ' + code, 'error');
            }
        } catch (error) {
            logDebug(`填充验证码出错: ${error.message}`);
            showNotification('填充验证码失败: ' + error.message, 'error');
        }
    }

    // 填充验证码到输入框 - 增强版

    // 监听页面加载完成 - 增强版初始化
    function ensureInitialization() {
        logDebug('确保扩展正确初始化...');

        // 检查是否已经初始化
        const existingPanel = document.getElementById('cursor-auto-panel');
        const existingButton = document.getElementById('cursor-auto-show-button');

        if (!existingPanel && !existingButton) {
            logDebug('未检测到现有面板或按钮，执行初始化');
            initializeExtension();
        } else {
            logDebug('检测到现有面板或按钮，跳过重复初始化');
        }
    }

    // 多重初始化策略，确保面板能够显示
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ensureInitialization);
    } else {
        ensureInitialization();
    }

    // 额外的延迟初始化，防止某些情况下面板不显示
    setTimeout(() => {
        const existingPanel = document.getElementById('cursor-auto-panel');
        const existingButton = document.getElementById('cursor-auto-show-button');

        // 修改：移除域名限制，在所有页面都进行延迟检查
        if (!existingPanel && !existingButton) {
            logDebug('延迟检查：面板未显示，强制初始化');
            initializeExtension();
        }
    }, 2000);

    // 专门针对推广页面的强化初始化
    if (window.location.href.includes('/promotions/')) {
        logDebug('推广页面强化初始化');

        // 立即检查
        setTimeout(() => {
            const existingPanel = document.getElementById('cursor-auto-panel');
            if (!existingPanel) {
                logDebug('推广页面面板不存在，强制创建');
                initializeExtension();
            }
        }, 500);

        // 再次检查
        setTimeout(() => {
            const existingPanel = document.getElementById('cursor-auto-panel');
            if (!existingPanel) {
                logDebug('推广页面面板仍不存在，再次强制创建');
                initializeExtension();
            }
        }, 3000);

        // 页面完全加载后的最终检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                const existingPanel = document.getElementById('cursor-auto-panel');
                if (!existingPanel) {
                    logDebug('推广页面加载完成后面板仍不存在，最终强制创建');
                    initializeExtension();
                }
            }, 1000);
        });
    }

    // 页面可见性变化时重新检查
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            setTimeout(() => {
                const existingPanel = document.getElementById('cursor-auto-panel');
                const existingButton = document.getElementById('cursor-auto-show-button');

                // 修改：移除域名限制，在所有页面都进行可见性检查
                if (!existingPanel && !existingButton) {
                    logDebug('页面重新可见：面板未显示，强制初始化');
                    initializeExtension();
                }
            }, 500);
        }
    });

    // 添加URL变化监听器
    let lastUrl = window.location.href;
    const urlChangeObserver = new MutationObserver(() => {
        if (window.location.href !== lastUrl) {
            logDebug(`检测到URL变化: ${lastUrl} -> ${window.location.href}`);
            lastUrl = window.location.href;
            
            // 当URL变为注册成功页面(onboarding)时，立即显示注册成功
            if (window.location.href.includes('/account/onboarding')) {
                logDebug('URL变化为注册成功页面，立即显示注册成功浮窗');
                setTimeout(() => {
                    createMainPanel();
                }, 300); // 短暂延迟确保DOM已加载
            }
            // 当URL变为注册页面时，立即更新浮窗
            else if (window.location.href.includes('/account/register')) {
                logDebug('URL变化为注册页面，立即更新浮窗');
                setTimeout(() => {
                    createMainPanel();
                }, 300); // 短暂延迟确保DOM已加载
            }
            // 当URL变为登录页面时，显示打赏浮窗
            else if (window.location.href.includes('/account/login')) {
                logDebug('URL变化为登录页面，显示打赏浮窗');
                setTimeout(() => {
                    createMainPanel();
                }, 300);
            }
            // 当URL变为Augment Code登录页面时，更新面板
            else if (window.location.href.includes('login.augmentcode.com')) {
                logDebug('URL变化为Augment Code登录页面，更新面板');
                setTimeout(() => {
                    createMainPanel();
                }, 300);
            }
            // 当URL变为Augment Code协议接受页面时，更新面板
            else if (window.location.href.includes('auth.augmentcode.com/terms-accept')) {
                logDebug('URL变化为Augment Code协议接受页面，显示协议确认界面');
                setTimeout(() => {
                    showTermsAcceptPanel();
                }, 300);
            }
        }
    });
    
    // 配置观察器
    urlChangeObserver.observe(document, { subtree: true, childList: true });
    
    // 在history API变化时也检测URL变化
    const originalPushState = history.pushState;
    history.pushState = function() {
        originalPushState.apply(this, arguments);
        logDebug('检测到history.pushState事件');
        if (window.location.href !== lastUrl) {
            logDebug(`通过history检测到URL变化: ${lastUrl} -> ${window.location.href}`);
            lastUrl = window.location.href;
            
            // 当URL变为注册成功页面(onboarding)时，立即显示注册成功
            if (window.location.href.includes('/account/onboarding')) {
                logDebug('URL变化为注册成功页面，立即显示注册成功浮窗');
                setTimeout(() => {
                    createMainPanel();
                }, 300); // 短暂延迟确保DOM已加载
            }
            // URL变为注册页面时立即更新浮窗
            else if (window.location.href.includes('/account/register')) {
                logDebug('URL变化为注册页面，立即更新浮窗');
                setTimeout(() => {
                    createMainPanel();
                }, 300);
            }
            // URL变为登录页面时显示打赏浮窗
            else if (window.location.href.includes('/account/login')) {
                logDebug('URL变化为登录页面，显示打赏浮窗');
                setTimeout(() => {
                    createMainPanel();
                }, 300);
            }
            // URL变为Augment Code登录页面时，更新面板
            else if (window.location.href.includes('login.augmentcode.com')) {
                logDebug('通过history检测到Augment Code登录页面，更新面板');
                setTimeout(() => {
                    createMainPanel();
                }, 300);
            }
            // URL变为Augment Code协议接受页面时，更新面板
            else if (window.location.href.includes('auth.augmentcode.com/terms-accept')) {
                logDebug('通过history检测到Augment Code协议接受页面，显示协议确认界面');
                setTimeout(() => {
                    showTermsAcceptPanel();
                }, 300);
            }
        }
    };

    // 显示通知
    function showNotification(message, title = "Augment Code 注册助手") {
        chrome.runtime.sendMessage({
            type: "notification",
            message: message,
            title: title
        });
    }

    /**
     * 自动填充登录表单并提交
     */
    function autoFillLoginForm() {
        console.debug("[Augment Code] 开始自动填充登录表单");
        
        // 获取保存的账号信息
        chrome.runtime.sendMessage({ type: "getAccountInfo" }, function(response) {
            const accountInfo = response?.accountInfo || null;
            
            // 检查是否有账号信息
            if (!accountInfo || !accountInfo.email || !accountInfo.password) {
                console.debug("[Augment Code] 无可用账号信息");
                updateInfoPanel(`
                    <h3 style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; color: #fff;">⚠️ 无可用账号信息</h3>
                    <p style="margin: 10px 0; color: #fff;">未找到保存的账号信息，请先注册或保存一个账号。</p>
                    <button id="go-back" style="width: 100%; margin-top: 10px; background: linear-gradient(135deg, #4776E6, #8E54E9) !important;">返回</button>
                `);
                document.getElementById("go-back").addEventListener("click", function() {
                    showLoginPagePanel();
                });
                return;
            }
            
            // 更新面板显示登录中状态
            updateInfoPanel(`
                <h3 style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; color: #fff;">⏱️ 登录中...</h3>
                <p style="margin: 10px 0; color: #fff;">正在使用账号 ${accountInfo.email} 登录中，请稍候...</p>
                <div style="width: 100%; height: 4px; background-color: rgba(255,255,255,0.2); margin: 10px 0; border-radius: 2px; overflow: hidden;">
                    <div class="login-progress" style="width: 0%; height: 100%; background: linear-gradient(90deg, #11998e, #38ef7d); transition: width 0.3s ease;"></div>
                </div>
            `);
            
            // 动画进度条
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += 5;
                if (progress > 95) progress = 95;
                const progressBar = document.querySelector('.login-progress');
                if (progressBar) progressBar.style.width = `${progress}%`;
            }, 200);
            
            // 记录当前URL，用于检测页面变化
            const initialUrl = window.location.href;
            console.debug(`[Augment Code] 当前登录页URL: ${initialUrl}`);
            
            // 设置URL监听，检测登录成功后的页面跳转
            const urlCheckInterval = setInterval(() => {
                const currentUrl = window.location.href;
                if (currentUrl !== initialUrl) {
                    console.debug(`[Augment Code] 检测到URL变化: ${initialUrl} -> ${currentUrl}`);
                    
                    // 如果URL变化且不在登录页，认为登录成功
                    if (!currentUrl.includes("/account/login")) {
                        console.debug(`[Augment Code] 登录成功，URL已不包含登录页路径`);
                        clearInterval(urlCheckInterval);
                        clearInterval(progressInterval);
                        
                        // 重置登录状态并更新面板
                        checkLoginStatusAndReset();
                    }
                }
            }, 500);
            
            // 15秒后清除URL检测定时器，防止无限期运行
            setTimeout(() => {
                clearInterval(urlCheckInterval);
                console.debug(`[Augment Code] URL监听超时自动清除`);
            }, 15000);
            
            // 填充表单
            try {
                // 查找并填充邮箱输入框
                const emailInput = document.querySelector('input[placeholder*="邮箱"], input[type="email"]');
                if (emailInput) {
                    setInputValue(emailInput, accountInfo.email);
                    console.debug("[Augment Code] 邮箱字段已填充");
                } else {
                    console.debug("[Augment Code] 未找到邮箱输入框");
                }
                
                // 查找并填充密码输入框
                const passwordInput = document.querySelector('input[placeholder*="密码"], input[type="password"]');
                if (passwordInput) {
                    setInputValue(passwordInput, accountInfo.password);
                    console.debug("[Augment Code] 密码字段已填充");
                } else {
                    console.debug("[Augment Code] 未找到密码输入框");
                }
                
                // 等待一下再点击登录按钮，确保输入值已应用
                setTimeout(() => {
                    // 查找并点击登录按钮
                    const loginButtonSelectors = [
                        'button[type="submit"]', 
                        'button.login-button', 
                        'button:contains("登录")',
                        'button:contains("Log in")',
                        'button:contains("Sign in")',
                        'form button' // form中的按钮
                    ];
                    
                    let loginButton = null;
                    
                    // 尝试通过多个选择器查找登录按钮
                    for (const selector of loginButtonSelectors) {
                        try {
                            const btn = document.querySelector(selector);
                            if (btn) {
                                loginButton = btn;
                                console.debug(`[Augment Code] 通过选择器 '${selector}' 找到登录按钮`);
                                break;
                            }
                        } catch (e) {
                            console.debug(`[Augment Code] 选择器 '${selector}' 查找出错: ${e.message}`);
                        }
                    }
                    
                    // 如果上面的选择器都没找到，尝试查找form中的唯一button
                    if (!loginButton) {
                        const form = document.querySelector('form');
                        if (form) {
                            const formButtons = form.querySelectorAll('button');
                            if (formButtons.length === 1) {
                                loginButton = formButtons[0];
                                console.debug("[Augment Code] 在表单中找到唯一按钮，可能是登录按钮");
                            } else if (formButtons.length > 1) {
                                // 如果有多个按钮，尝试找到有提交特征的那个
                                for (const btn of formButtons) {
                                    if (btn.type === "submit" || 
                                        btn.textContent.toLowerCase().includes("log") ||
                                        btn.textContent.toLowerCase().includes("sign") ||
                                        btn.textContent.includes("登录")) {
                                        loginButton = btn;
                                        console.debug("[Augment Code] 在表单多个按钮中找到最可能的登录按钮");
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    
                    if (loginButton) {
                        console.debug("[Augment Code] 找到登录按钮，准备点击");
                        // 直接调用click方法
                        loginButton.click();
                        
                        // 如果点击可能被阻止，尝试创建并触发鼠标事件
                        setTimeout(() => {
                            try {
                                console.debug("[Augment Code] 尝试通过MouseEvent触发点击");
                                loginButton.dispatchEvent(new MouseEvent('click', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                }));
                            } catch (e) {
                                console.debug(`[Augment Code] MouseEvent触发失败: ${e.message}`);
                            }
                            
                            // 如果是表单内的按钮，尝试提交表单
                            const parentForm = loginButton.closest('form');
                            if (parentForm) {
                                try {
                                    console.debug("[Augment Code] 尝试触发表单提交事件");
                                    // 只使用事件触发形式，避免直接调用submit()方法
                                    parentForm.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                                    console.debug("[Augment Code] 已触发表单提交事件");
                                    
                                    // 不再调用form.submit()，避免"form not connected"错误
                                } catch (e) {
                                    console.debug(`[Augment Code] 表单事件触发失败: ${e.message}`);
                                    // 忽略错误继续执行
                                }
                            }
                        }, 100);
                    } else {
                        console.debug("[Augment Code] 未找到登录按钮");
                        clearInterval(progressInterval);
                        
                        updateInfoPanel(`
                            <h3 style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; color: #fff;">❌ 操作失败</h3>
                            <p style="margin: 10px 0; color: #fff;">未找到登录按钮，无法完成自动登录。</p>
                            <button id="go-back" style="width: 100%; margin-top: 10px; background: linear-gradient(135deg, #4776E6, #8E54E9) !important;">返回</button>
                        `);


                        document.getElementById("go-back").addEventListener("click", function() {
                            showLoginPagePanel();
                        });
                    }
                }, 500);
            } catch (error) {
                console.error("[Augment Code] 自动填充登录表单时出错:", error);
                clearInterval(progressInterval);
                
                updateInfoPanel(`
                    <h3 style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; color: #fff;">❌ 操作失败</h3>
                    <p style="margin: 10px 0; color: #fff;">自动填充登录表单时发生错误: ${error.message}</p>
                    <button id="go-back" style="width: 100%; margin-top: 10px; background: linear-gradient(135deg, #4776E6, #8E54E9) !important;">返回</button>
                `);


                document.getElementById("go-back").addEventListener("click", function() {
                    showLoginPagePanel();
                });
            }
        });
    }

    // 添加辅助函数用于在登录页显示面板
    function showLoginPagePanel() {
        chrome.runtime.sendMessage({ type: "getAccountInfo" }, function(response) {
            const accountInfo = response?.accountInfo || null;
            const hasAccountInfo = accountInfo && accountInfo.email && accountInfo.password;
            
            // 添加浮窗
            updateInfoPanel(`
                <h3 style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; color: #fff;">👋 欢迎使用Augment Code助手</h3>

                <p style="margin: 10px 0 5px 0; font-size: 14px; color: #fff;">如果本工具对您有帮助，欢迎打赏支持作者~</p>
                <div style="display: flex; justify-content: space-around; margin: 10px 0;">
                    <div style="text-align: center; margin: 0 5px;">
                        <img src="https://www.liubao.org.cn/image/wx.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                        <p style="margin: 5px 0; font-size: 12px; color: #fff;">微信</p>
                    </div>
                    <div style="text-align: center; margin: 0 5px;">
                        <img src="https://www.liubao.org.cn/image/zfb.png" class="tip-qrcode-img" style="transition: transform 0.3s ease; width: 100%; height: auto; max-width: 130px; display: block; margin: 0 auto; border-radius: 4px;">
                        <p style="margin: 5px 0; font-size: 12px; color: #fff;">支付宝</p>
                    </div>
                </div>
                <p style="margin: 10px 0 0 0; font-size: 12px; color: #FFD700; text-align: center;">💡 扫码打赏并留言获取专属口令</p>
                
                <div style="margin: 10px 0; color: white; font-size: 14px; border-top: 1px solid rgba(255,255,255,0.2); padding-top: 10px;">
                    ${hasAccountInfo ? `
                        <p style="margin: 5px 0; color: #fff;">您已有可用账号:</p>
                        <p style="margin: 5px 0; font-size: 13px; word-break: break-all; color: #fff;">📧 ${accountInfo.email}</p>
                        <p style="margin: 5px 0; font-size: 13px; color: #fff;">🔑 ${accountInfo.password || '未设置密码'}</p>
                    ` : `
                        <p style="margin: 5px 0; color: #fff;">您还没有已保存的账号</p>
                        <p style="margin: 5px 0; font-size: 13px; color: #fff;">可以选择注册新账号或登录已有账号</p>
                    `}
                </div>
                
                <div style="margin-top: 10px; width: 100%;">
                    <button id="auto-login" style="width: 100%; margin-bottom: 5px; background: linear-gradient(135deg, #11998e, #38ef7d) !important; ${!hasAccountInfo ? 'opacity: 0.5;' : ''}">使用已有账号登录</button>
                    <button id="go-to-register" style="width: 100%; margin-bottom: 5px; background: linear-gradient(135deg, #4776E6, #8E54E9) !important;">前往注册 Augment Code</button>
                </div>
            `);
            
            // 添加悬停放大效果
            const style = document.createElement('style');
            style.id = 'tip-qrcode-style';
            style.textContent = `
                .tip-qrcode-img:hover {
                    transform: scale(1.5);
                    z-index: 10000;
                }
            `;
            if (!document.getElementById('tip-qrcode-style')) {
                document.head.appendChild(style);
            }
            
            // 添加前往注册按钮功能
            const goToRegisterButton = document.getElementById("go-to-register");
            if (goToRegisterButton) {
                goToRegisterButton.addEventListener("click", function() {
                    window.location.href = "https://login.augmentcode.com/u/login/identifier";
                });
            }
            
            // 添加自动登录按钮功能
            const autoLoginButton = document.getElementById("auto-login");
            if (autoLoginButton) {
                if (hasAccountInfo) {
                    autoLoginButton.addEventListener("click", function() {
                        autoFillLoginForm();
                    });
                } else {
                    autoLoginButton.disabled = true;
                }
            }
        });
    }

    // 修改：在所有页面都进行初始化检测
    const currentPageUrl = window.location.href;
    console.debug("[Augment Code] 初始化时检测页面:", currentPageUrl);

    // 在所有页面都调用初始化函数
    initializeExtension();
})();

// 添加在代码末尾，确保浮窗按钮能正常显示和隐藏
// 添加以下代码：

// 添加DOM完全加载后的检查
document.addEventListener('DOMContentLoaded', function() {
    console.debug("[Augment Code] DOM加载完成，确保按钮正常显示");
    
    // 检查按钮显示状态
    const showButton = document.getElementById('cursor-auto-show-button');
    const panel = document.getElementById('cursor-auto-panel');
    
    if (showButton && panel) {
        // 如果面板不显示，确保按钮显示
        if (panel.style.display === 'none' || !panel || !document.body.contains(panel)) {
            showButton.style.cssText = 'display: block !important;';
            console.debug("[Augment Code] 面板不显示，设置按钮为显示状态");
        } else {
            showButton.style.cssText = 'display: none !important;';
            console.debug("[Augment Code] 面板显示，设置按钮为隐藏状态");
        }
    }
});

// 添加周期性检查，确保按钮状态始终正确
setInterval(function() {
    const showButton = document.getElementById('cursor-auto-show-button');
    const panel = document.getElementById('cursor-auto-panel');
    
    if (showButton && panel) {
        // 检查面板是否显示
        const panelVisible = panel.style.display !== 'none' && document.body.contains(panel);
        // 检查按钮是否显示
        const buttonVisible = showButton.style.display !== 'none' && getComputedStyle(showButton).display !== 'none';
        
        // 确保按钮和面板状态相反
        if (panelVisible && buttonVisible) {
            // 如果面板和按钮都显示，隐藏按钮
            showButton.style.cssText = 'display: none !important;';
            console.debug("[Augment Code] 修正：面板和按钮都显示，隐藏按钮");
        } else if (!panelVisible && !buttonVisible) {
            // 如果面板和按钮都不显示，显示按钮
            showButton.style.cssText = 'display: block !important;';
            console.debug("[Augment Code] 修正：面板和按钮都不显示，显示按钮");
        }
    }
}, 1000); // 每秒检查一次

