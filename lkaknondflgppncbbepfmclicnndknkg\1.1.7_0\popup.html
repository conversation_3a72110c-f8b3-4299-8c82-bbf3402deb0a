<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Augment Code 注册助手</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      width: 320px;
      padding: 10px;
      margin: 0;
      font-size: 13px;
    }
    h2 {
      color: #333;
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 16px;
    }
    h3 {
      margin: 0 0 8px 0;
      font-size: 14px;
    }
    .section {
      margin-bottom: 10px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    label {
      display: inline-block;
      margin-bottom: 3px;
      font-weight: bold;
      font-size: 12px;
    }
    .field-row {
      display: flex;
      margin-bottom: 5px;
      align-items: center;
    }
    .field-row label {
      width: 100px;
      margin-bottom: 0;
      margin-right: 5px;
    }
    input[type="text"],
    input[type="number"],
    select {
      width: 100%;
      padding: 5px;
      margin-bottom: 5px;
      border: 1px solid #ddd;
      border-radius: 3px;
      box-sizing: border-box;
      font-size: 12px;
    }
    input[type="checkbox"] {
      margin-right: 5px;
    }
    .checkbox-row {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }
    .checkbox-row label {
      font-weight: normal;
      margin: 0;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 6px 12px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 13px;
      margin: 3px 2px;
      cursor: pointer;
      border-radius: 3px;
      min-width: 70px;
    }
    button.danger {
      background-color: #f44336;
    }
    button.info {
      background-color: #2196F3;
    }
    .token-display {
      background-color: #f5f5f5;
      padding: 5px;
      border-radius: 3px;
      margin-top: 5px;
      word-break: break-all;
      font-size: 11px;
    }
    textarea {
      width: 100%;
      height: 60px;
      margin-top: 3px;
      resize: vertical;
      font-size: 12px;
    }
    .hidden {
      display: none;
    }
    .header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      cursor: move;
      -webkit-app-region: drag;
      padding: 5px 0;
    }
    .header-actions h2 {
      margin: 0;
      -webkit-user-select: none;
      user-select: none;
    }
    .header-actions button {
      -webkit-app-region: no-drag;
      cursor: pointer;
    }
    .button-row {
      display: flex;
      gap: 10px;
      margin-top: 8px;
      flex-wrap: wrap;
    }
    .compact-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 5px;
    }
    .compact-info p {
      margin: 3px 0;
      font-size: 13px;
    }
    .token-area {
      grid-column: span 2;
    }
    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
    }
    .form-grid .field-row {
      margin-bottom: 0;
    }
    .tab-content {
      max-height: 300px;
      overflow-y: auto;
    }
    #tokenSection {
      margin-top: 0;
      padding-top: 0;
    }
    .token-info-panel {
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 8px;
      background-color: #f9f9f9;
      margin-bottom: 10px;
    }
    .token-info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      align-items: center;
    }
    .token-info-row span {
      font-weight: bold;
    }
    .token-actions {
      display: flex;
      gap: 10px;
      margin-top: 8px;
      justify-content: center;
    }
    .token-actions button {
      flex: 1;
      max-width: 120px;
    }
  </style>
</head>
<body>
  <div class="header-actions" id="dragHandle">
    <h2>Augment Code 注册助手</h2>
  </div>
  
  <div class="section">
    <h3>邮箱配置</h3>
    <div class="form-grid">
      <div class="field-row">
        <label for="emailPrefix">临时邮箱前缀:</label>
        <input type="text" id="emailPrefix">
      </div>
      
      <div class="field-row">
        <label for="emailDomain">注册邮箱域名:</label>
        <input type="text" id="emailDomain">
      </div>
      
      <div class="field-row">
        <label for="realEmailDomain">临时邮箱域名:</label>
        <input type="text" id="realEmailDomain">
      </div>
      
      <div class="field-row">
        <label for="emailCodeType">验证码获取:</label>
        <select id="emailCodeType">
          <option value="AUTO">自动获取</option>
          <option value="INPUT">手动输入</option>
        </select>
      </div>
      
      <div class="field-row">
        <label for="emailPin">邮箱PIN码:</label>
        <input type="text" id="emailPin" placeholder="可选">
      </div>
      
      <div class="field-row">
        <label for="tempEmailApi">TempMail API:</label>
        <input type="text" id="tempEmailApi">
      </div>
      
      <div class="field-row">
        <label for="emailVerificationRetries">最大重试次数:</label>
        <input type="number" id="emailVerificationRetries" min="1" max="20">
      </div>
      
      <div class="field-row">
        <label for="emailVerificationWait">重试等待(秒):</label>
        <input type="number" id="emailVerificationWait" min="1" max="60">
      </div>
    </div>
  </div>
  
  <div class="section">
    <h3>功能设置</h3>
    <div class="form-grid">
      <div class="checkbox-row">
        <input type="checkbox" id="autoFill">
        <label for="autoFill">自动填写表单</label>
      </div>
    </div>
  </div>
  
  <div class="section" id="tokenSection">
    <h3>Token信息</h3>
    <div id="tokenInfo" class="hidden">
      <div class="token-info-panel">
        <div class="token-info-row">
          <div><strong>邮箱:</strong></div>
          <div id="emailDisplay">-</div>
        </div>
        <div class="token-info-row">
          <div><strong>密码:</strong></div>
          <div id="passwordDisplay">-</div>
        </div>
        <div class="token-info-row">
          <div><strong>用户:</strong></div>
          <div id="userDisplay">-</div>
        </div>
      </div>
      <p><strong>Token:</strong></p>
      <textarea id="tokenDisplay" readonly></textarea>
      <div class="token-actions">
        <button id="copyToken" class="info">复制Token</button>
        <button id="saveToAccounts" class="info">保存账号</button>
      </div>
    </div>
    <div id="noTokenInfo">
      <p>暂无Token信息，请完成注册获取。</p>
      <button id="goToSignup" class="info">前往 Augment Code 登录页面</button>
    </div>
  </div>
  
  <div class="section">
    <div class="button-row">
      <button id="saveConfig">保存配置</button>
      <button id="clearData" class="danger">清除账号数据</button>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html> 