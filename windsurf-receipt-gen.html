<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windsurf付费凭证生成器</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 700;
        }
        
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input[type="email"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        input[type="email"]:focus {
            outline: none;
            border-color: #4A90E2;
        }
        
        .btn {
            background: linear-gradient(135deg, #4A90E2, #357ABD);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .note {
            margin-top: 20px;
            padding: 15px;
            background: rgba(74, 144, 226, 0.1);
            border-left: 4px solid #4A90E2;
            border-radius: 4px;
            text-align: left;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌊 Windsurf付费凭证生成器</h1>
        <form id="receiptForm">
            <div class="form-group">
                <label for="email">邮箱地址：</label>
                <input type="email" id="email" name="email" required placeholder="请输入您的邮箱地址">
            </div>
            <button type="submit" class="btn">🎫 生成Windsurf付费凭证</button>
        </form>
        <div class="note">
            <strong>说明：</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>生成的凭证包含随机的发票信息</li>
                <li>金额随机为 $6.90 或 $15.00</li>
                <li>可用于打印或保存为PDF</li>
                <li>所有数据均为模拟生成</li>
            </ul>
        </div>
    </div>

    <script>
        // 绑定全局函数到window对象
        window.generateReceipt = generateReceipt;
        
        document.getElementById('receiptForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            if (email) {
                generateReceipt(email);
            }
        });

        function generateReceipt(email) {
            const invoiceData = generateRandomWindsurfInvoice(email);
            const htmlContent = createWindsurfInvoiceHTML(invoiceData);
            
            const newWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            newWindow.document.write(htmlContent);
            newWindow.document.close();
        }

        function generateRandomWindsurfInvoice(email) {
            const invoiceNumber = generateInvoiceNumber();
            const receiptNumber = generateReceiptNumber();
            const datePaid = generateRandomDate();
            const paymentMethod = generatePaymentMethod();
            const billTo = generateBillToInfo(email);
            
            // 随机选择金额：要么$6.90要么$15.00
            const amounts = ['$6.90', '$15.00'];
            const amount = amounts[Math.floor(Math.random() * amounts.length)];
            
            const description = 'Windsurf Pro';
            const dateRange = generateDateRange(datePaid);

            return {
                invoiceNumber,
                receiptNumber,
                datePaid,
                paymentMethod,
                billTo,
                amount,
                description,
                dateRange
            };
        }

        function generateInvoiceNumber() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 8; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result + '-' + String(Math.floor(Math.random() * 9000) + 1000).padStart(4, '0');
        }

        function generateReceiptNumber() {
            return String(Math.floor(Math.random() * 9000) + 1000) + '-' + String(Math.floor(Math.random() * 9000) + 1000);
        }

        function generateRandomDate() {
            const start = new Date(2024, 0, 1);
            const end = new Date(2025, 11, 31);
            const randomTime = start.getTime() + Math.random() * (end.getTime() - start.getTime());
            const randomDate = new Date(randomTime);
            
            const months = ['January', 'February', 'March', 'April', 'May', 'June',
                          'July', 'August', 'September', 'October', 'November', 'December'];
            
            return `${months[randomDate.getMonth()]} ${randomDate.getDate()}, ${randomDate.getFullYear()}`;
        }

        function generatePaymentMethod() {
            const methods = ['Visa ending in 1234', 'Mastercard ending in 5678', 'American Express ending in 9012'];
            return methods[Math.floor(Math.random() * methods.length)];
        }

        function generateBillToInfo(email) {
            const names = ['John Smith', 'Jane Doe', 'Michael Johnson', 'Sarah Wilson', 'David Brown'];
            const addresses1 = ['123 Main St', '456 Oak Ave', '789 Pine Rd', '321 Elm St', '654 Maple Dr'];
            const addresses2 = ['Apt 101', 'Suite 200', 'Unit 5B', 'Floor 3', 'Room 12A'];
            const countries = ['United States', 'Canada', 'United Kingdom', 'Australia', 'Germany'];
            
            return {
                name: names[Math.floor(Math.random() * names.length)],
                address1: addresses1[Math.floor(Math.random() * addresses1.length)],
                address2: addresses2[Math.floor(Math.random() * addresses2.length)],
                country: countries[Math.floor(Math.random() * countries.length)],
                email: email
            };
        }

        function generateDateRange(datePaid) {
            const date = new Date(datePaid);
            const startDate = new Date(date);
            const endDate = new Date(date);
            endDate.setMonth(endDate.getMonth() + 1);
            
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            
            const startStr = `${months[startDate.getMonth()]} ${startDate.getDate()}`;
            const endStr = `${months[endDate.getMonth()]} ${endDate.getDate()}, ${endDate.getFullYear()}`;
            
            return `${startStr} - ${endStr}`;
        }

        function createWindsurfInvoiceHTML(data) {
            let html = '<!DOCTYPE html><html lang="en"><head>';
            html += '<meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">';
            html += '<title>Receipt - ' + data.invoiceNumber + '</title>';
            html += '<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">';
            html += '<style>';
            html += 'body{margin:0;padding:0;background-color:#f3f4f6;display:flex;justify-content:center;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}';
            html += '.page{width:210mm;min-height:297mm;margin:2cm 0;padding:40px 50px;box-shadow:0 0 1cm rgba(0,0,0,0.3);background-color:#ffffff;display:flex;flex-direction:column;box-sizing:border-box;font-family:\'Inter\',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Helvetica,Arial,sans-serif;color:#000000}';
            html += '.content-wrapper{flex-grow:1}';
            html += '.header{display:flex;justify-content:space-between;align-items:center}';
            html += '.header h1{font-size:28px;font-weight:700;margin:0;line-height:1}';
            html += '.logo{display:flex;align-items:center;gap:12px;justify-content:flex-end;margin-right:-25px}';
            html += '.logo img{width:auto;height:80px}';
            html += '.invoice-info table{border-collapse:collapse}';
            html += '.invoice-info td{padding:1px 0;vertical-align:top;line-height:1.6;font-size:13px}';
            html += '.invoice-info td:first-child{color:#000000;padding-right:20px;font-weight:400}';
            html += '.invoice-info td:last-child{font-weight:500}';
            html += '.address-section{display:flex;justify-content:flex-start;gap:120px;margin-bottom:20px;font-size:13px}';
            html += '.address p{margin:0;line-height:1.6}';
            html += '.summary{margin-bottom:40px}';
            html += '.summary h2{font-size:16px;font-weight:700;margin:0}';
            html += '.items-table{width:100%;border-collapse:collapse;font-size:13px}';
            html += '.items-table thead th{text-align:right;border-bottom:1px solid #9ca3af;padding-bottom:8px;font-weight:400;color:#000000;font-size:12px}';
            html += '.items-table thead th:first-child{text-align:left}';
            html += '.items-table th:nth-child(2),.items-table td:nth-child(2){width:15%}';
            html += '.items-table th:nth-child(3),.items-table td:nth-child(3){width:15%}';
            html += '.items-table th:nth-child(4),.items-table td:nth-child(4){width:13%}';
            html += '.items-table td{text-align:left}';
            html += '.items-table td:nth-child(2),.items-table td:nth-child(3),.items-table td:nth-child(4){text-align:right}';
            html += '.item-description{color:#000000}';
            html += '.item-date{font-size:12px;margin-top:2px}';
            html += '.totals-section{display:flex;justify-content:flex-end}';
            html += '.totals-table{width:350px;font-size:13px}';
            html += '.totals-table td{padding:1px 0}';
            html += '.totals-table td:last-child{text-align:right;font-weight:500}';
            html += '.totals-table tr td{border-top:1px solid #e5e7eb}';
            html += '.totals-table tr:last-child td{font-weight:700}';
            html += '.footer{margin-top:auto;padding-top:15px;border-top:1px solid #e5e7eb;display:flex;justify-content:space-between;font-size:11px}';
            html += '@media print{body{margin:0;background:white}.page{box-shadow:none}.footer{margin-top:380px;page-break-inside:avoid}}';
            html += '</style></head><body>';
            html += '<div class="page">';
            html += '<div class="content-wrapper">';
            html += '<div class="header"><h1>Receipt</h1><div class="logo"><img src="https://liubao.org.cn/image/windsurf1.png" alt="Windsurf Logo" /></div></div>';
            html += '<div class="invoice-info"><table>';
            html += '<tr><td><b>Invoice number:</b></td><td><b>' + data.invoiceNumber + '</b></td></tr>';
            html += '<tr><td><b>Receipt number:</b></td><td><b>' + data.receiptNumber + '</b></td></tr>';
            html += '<tr><td><b>Date paid:</b></td><td><b>' + data.datePaid + '</b></td></tr>';
            html += '<tr><td><b>Payment method:</b></td><td><b>' + data.paymentMethod + '</b></td></tr>';
            html += '</table></div>';
            html += '<div class="address-section">';
            html += '<div><p><b>Windsurf</b></p><p>900 Villa Street</p>';
            html += '<p>Mountain View, California 94041</p><p>United States</p>';
            html += '<p><EMAIL></p><p>US EIN 87-1068811</p></div>';
            html += '<div><p><b>Bill to:</b></p><p>' + data.billTo.name + '</p>';
            html += '<p>' + data.billTo.address1 + '</p><p>' + data.billTo.address2 + '</p>';
            html += '<p>' + data.billTo.country + '</p><p>' + data.billTo.email + '</p></div>';
            html += '</div>';
            html += '<div class="summary"><h2>' + data.amount + ' paid on ' + data.datePaid + '</h2></div>';
            html += '<table class="items-table">';
            html += '<thead><tr><th>Description</th><th>Qty</th><th>Unit price</th><th>Amount</th></tr></thead>';
            html += '<tbody><tr><td><div class="item-description">' + data.description + '</div>';
            html += '<div class="item-date">' + data.dateRange + '</div></td>';
            html += '<td>1</td><td>' + data.amount + '</td><td><b>' + data.amount + '</b></td></tr></tbody>';
            html += '</table>';
            html += '<div class="totals-section">';
            html += '<table class="totals-table">';
            html += '<tbody>';
            html += '<tr><td>Subtotal</td><td>' + data.amount + '</td></tr>';
            html += '<tr><td>Total</td><td>' + data.amount + '</td></tr>';
            html += '<tr><td><b>Amount paid</b></td><td><b>' + data.amount + '</b></td></tr>';
            html += '</tbody>';
            html += '</table></div>';
            html += '</div>';
            html += '<div class="footer">';
            html += '<span>' + data.receiptNumber + ' - ' + data.amount + ' paid on ' + data.datePaid + '</span>';
            html += '<span>Page 1 of 1</span>';
            html += '</div></div>';
            html += '</body></html>';
            return html;
        }
    </script>
</body>
</html>
