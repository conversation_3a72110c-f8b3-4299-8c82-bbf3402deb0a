# Augment Code 注册助手

这是一个Chrome浏览器扩展，专门用于自动化 Augment Code 的注册流程。

## 主要功能

- 自动生成临时邮箱地址
- 自动填写 Augment Code 登录页面的邮箱
- 自动点击 Continue 按钮
- 自动获取邮箱验证码
- 支持临时邮箱服务自动解析验证码
- 提供浮动控制面板，方便操作

## 安装方法

### 开发者模式安装（推荐用于开发和测试）

1. 下载本扩展的压缩包并解压到本地
2. 打开Chrome浏览器，在地址栏输入 `chrome://extensions/`
3. 右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择解压后的文件夹

### Chrome Web Store上传（用于发布）

**原始版本**（便于调试）：
```bash
./package.sh
# 上传 augment-code-extension.zip
```

**混淆版本**（保护代码）：
```bash
./obfuscate.sh
cd obfuscated
./package.sh
# 上传 augment-code-extension-obfuscated.zip
```

## 使用方法

1. 安装扩展后
2. 访问 [Augment Code登录页面](https://login.augmentcode.com/)
3. 扩展会自动显示操作面板
4. 点击"生成邮箱地址"按钮创建临时邮箱
5. 点击"填写邮箱"按钮自动填充邮箱并点击 Continue
6. 等待页面跳转到验证码页面后，点击"获取验证码"按钮

### 功能设置

- **自动填写表单**：是否自动填写注册表单

## 项目结构

```
augmentcode-register/
├── manifest.json        # 扩展配置文件
├── content.js           # 主要内容脚本
├── background.js        # 后台脚本
├── popup.html           # 弹出窗口HTML
├── popup.js             # 弹出窗口交互脚本
├── icons/               # 扩展图标文件夹
├── package.sh           # 扩展打包脚本
├── obfuscate.sh         # 代码混淆脚本
├── obfuscated/          # 混淆后的文件目录（运行混淆脚本后生成）
└── README.md            # 说明文档
```

## 构建和打包工具

### 1. package.sh - 扩展打包脚本
**用途**：将扩展文件打包成可上传到Chrome Web Store的zip文件

**使用方法**：
```bash
# 给脚本执行权限并运行
chmod +x package.sh
./package.sh
```

**功能**：
- 自动创建 `augment-code-extension.zip` 文件
- 确保 manifest.json 在zip包根目录
- 包含所有必要的扩展文件
- 显示打包后的文件列表和大小

### 2. obfuscate.sh - 代码混淆脚本
**用途**：对JavaScript代码进行混淆，保护源代码逻辑

**使用方法**：
```bash
# 给脚本执行权限并运行
chmod +x obfuscate.sh
./obfuscate.sh

# 打包混淆版本
cd obfuscated
./package.sh
```

**功能**：
- 混淆函数名和变量名（如：`logDebug` → `_0x1a2b`）
- 编码中文字符串（如：`"联系作者"` → `"\x8054\x7cfb\x4f5c\x8005"`）
- 移除注释和多余空白，压缩代码体积
- 添加干扰代码增加逆向难度
- 保留重要的Chrome API和DOM接口不被混淆
- 生成混淆版本的打包脚本

**混淆效果**：
- content.js：314KB → 19KB（压缩94%）
- background.js：47KB → 21KB（压缩55%）
- popup.js：6KB → 5KB（压缩11%）

**注意事项**：
- 混淆后代码难以调试，建议保留原始版本
- 功能完全兼容，不影响扩展正常运行
- 适合上传到商店时保护代码知识产权

### 核心功能模块

- **邮箱生成模块**：自动生成临时邮箱地址
- **自动表单填写模块**：自动填写 Augment Code 登录表单
- **验证码处理模块**：自动获取和填写邮箱验证码
- **界面交互模块**：提供浮动控制面板和状态显示

## 适配说明

本扩展专门适配 Augment Code 的注册流程：
- 检测 `https://login.augmentcode.com/` 页面
- 自动填充邮箱到指定的输入框：`input[name="username"]`
- 自动点击 Continue 按钮：`button[type="submit"][name="action"][value="default"]`
- 移除了原有的姓名和密码生成逻辑，专注于邮箱注册流程

### 常见问题

#### 邮箱生成失败?
- 检查网络连接
- 确保临时邮箱服务可用

#### 自动填写不工作?
- 刷新页面重试
- 确保在正确的 Augment Code 登录页面
- 点击插件提供的"重试"按钮

#### 验证码获取失败?
- 等待几秒后重试
- 检查邮箱服务是否正常
- 某些时候可能需要手动输入验证码

## 注意事项

- 某些时候验证码可能无法自动获取，此时请手动输入
- 本插件仅供学习和研究使用
- 请确保网络连接正常
- 建议在使用前测试邮箱服务的可用性

## 隐私声明

本扩展不会收集或发送您的个人信息到任何第三方服务器。所有生成的账号信息仅保存在本地浏览器中，仅用于自动填写表单。