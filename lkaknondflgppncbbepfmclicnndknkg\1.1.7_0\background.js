// 引入MD5库
importScripts('md5.min.js');

// 可用的邮箱域名列表
const AVAILABLE_EMAIL_DOMAINS = [
  "niubea.site",
  "niubea.fun",
  "niubea.online",
  "niubea.xyz"
];

// 随机选择一个邮箱域名
function getRandomEmailDomain() {
  const randomIndex = Math.floor(Math.random() * AVAILABLE_EMAIL_DOMAINS.length);
  return AVAILABLE_EMAIL_DOMAINS[randomIndex];
}

// 扩展初始化
chrome.runtime.onInstalled.addListener(() => {
  // 设置默认配置
  chrome.storage.sync.get('augmentCodeConfig', (result) => {
    if (!result.augmentCodeConfig) {
      const defaultConfig = {
        emailPrefix: "", // 临时邮箱前缀
        emailDomain: "niubea.site", // 注册时使用的邮箱域名
        realEmailDomain: "niubea.site", // 实际收取邮件时使用的域名
        autoFill: true, // 是否自动填写表单
        emailCodeType: "AUTO", // 验证码获取方式，AUTO或INPUT
        emailPin: "", // 邮箱PIN码，tempmail需要
        tempEmailApi: "https://mail.niubea.site", // TempMail API地址
        emailVerificationRetries: 5, // 最大重试次数
        emailVerificationWait: 10, // 每次重试等待时间(秒)
        enableLogging: true, // 是否启用日志打印，默认启用
      };
      chrome.storage.sync.set({ augmentCodeConfig: defaultConfig });
    }
  });
});

// 监听插件图标点击事件
chrome.action.onClicked.addListener((tab) => {
  // 在新标签页中打开 Augment Code 网站
  chrome.tabs.create({ url: 'https://augmentcode.com' });
  log('用户点击插件图标，已打开 Augment Code 网站');
});

// 日志打印函数
function log(message, ...args) {
  chrome.storage.sync.get('augmentCodeConfig', (result) => {
    if (result.augmentCodeConfig && result.augmentCodeConfig.enableLogging) {
      // console.log(`[Augment Code助手后台] ${message}`, ...args);
    }
  });
}

// 错误日志打印函数
function error(message, ...args) {
  chrome.storage.sync.get('augmentCodeConfig', (result) => {
    if (result.augmentCodeConfig && result.augmentCodeConfig.enableLogging) {
      // console.error(`[Augment Code助手后台] ${message}`, ...args);
    }
  });
}

// 获取动态口令
async function getDynamicCode() {
  try {
    log('开始获取动态口令');

    const apiUrl = 'https://liubao.org.cn/index/augkl';
    log(`使用API: ${apiUrl}`);

    // 发送请求获取动态口令
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
      },
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'omit',
      timeout: 10000
    });

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }

    const data = await response.json();
    log(`动态口令API响应:`, data);

    // 解析动态口令 - augkl接口验证规则：'_liubao' + 用户输入内容进行MD5加密后与API返回的data字段比对
    if (data.code === 0 && data.data) {
      const dynamicCode = data.data;
      log(`成功获取动态口令: ${dynamicCode}`);
      return dynamicCode;
    } else {
      throw new Error(`API返回错误: ${data.msg || '未知错误'}`);
    }
  } catch (error) {
    error(`获取动态口令失败: ${error.message}`);
    throw error;
  }
}

// 验证augkl动态口令（基于用户输入）
async function verifyAugklCode(userInputCode) {
  try {
    log(`开始验证augkl动态口令，用户输入: "${userInputCode}"`);

    const apiUrl = 'https://liubao.org.cn/index/augkl';
    log(`使用augkl验证API: ${apiUrl}`);

    // augkl接口验证规则：'_liubao' + 用户输入内容进行MD5加密后与API返回的data字段比对
    const combinedString = '_liubao+' + userInputCode;
    const combinedMd5 = md5(combinedString);
    log(`拼接字符串: "${combinedString}"`);
    log(`拼接字符串MD5: ${combinedMd5}`);

    // 发送请求验证动态口令
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
      },
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'omit',
      timeout: 10000
    });

    log(`HTTP响应状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    log(`augkl验证API响应:`, data);
    log(`响应数据详情: code=${data.code}, data="${data.data}", msg="${data.msg}"`);

    // 验证返回的data字段是否与本地生成的MD5值一致
    if (data.code === 0 && data.data === combinedMd5) {
      log(`✅ augkl动态口令验证成功 - 本地MD5"${combinedMd5}"与接口返回"${data.data}"匹配`);
      return { success: true, message: '验证成功' };
    } else {
      log(`❌ augkl动态口令验证失败:`);
      log(`  本地MD5: "${combinedMd5}"`);
      log(`  接口返回: code=${data.code}, data="${data.data}"`);
      log(`  消息: ${data.msg || '无消息'}`);

      if (data.code !== 0) {
        return { success: false, message: `接口返回错误: ${data.msg || '未知错误'}` };
      } else {
        return { success: false, message: '验证口令不正确，请检查输入' };
      }
    }
  } catch (error) {
    error(`验证augkl动态口令失败: ${error.message}`);
    throw error;
  }
}

// 验证付费口令（基于用户输入）
async function verifyPaymentCode(userInputCode) {
  try {
    log(`开始验证付费口令，用户输入: "${userInputCode}"`);

    const apiUrl = 'https://liubao.org.cn/index/augpz';
    log(`使用付费验证API: ${apiUrl}`);

    // augpz接口验证规则：直接对用户输入内容进行MD5加密（不进行任何字符串拼接）
    const userInputMd5 = md5(userInputCode);
    log(`用户输入MD5: ${userInputMd5}`);

    // 发送请求验证付费口令
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
      },
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'omit',
      timeout: 10000
    });

    log(`HTTP响应状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    log(`付费验证API响应:`, data);
    log(`响应数据详情: code=${data.code}, data="${data.data}", msg="${data.msg}"`);

    // 验证返回的data字段是否与本地生成的MD5值一致
    if (data.code === 0 && data.data === userInputMd5) {
      log(`✅ 付费口令验证成功 - 本地MD5"${userInputMd5}"与接口返回"${data.data}"匹配`);
      return { success: true, message: '验证成功' };
    } else {
      log(`❌ 付费口令验证失败:`);
      log(`  本地MD5: "${userInputMd5}"`);
      log(`  接口返回: code=${data.code}, data="${data.data}"`);
      log(`  消息: ${data.msg || '无消息'}`);

      if (data.code !== 0) {
        return { success: false, message: `接口返回错误: ${data.msg || '未知错误'}` };
      } else {
        return { success: false, message: '验证口令不正确，请检查输入' };
      }
    }
  } catch (error) {
    error(`验证付费口令失败: ${error.message}`);
    log(`错误详情: ${error.stack || error.toString()}`);
    return { success: false, message: `网络错误: ${error.message}` };
  }
}

// 创建邮箱
async function createEmail(apiUrl, emailName) {
  // 最多重试2次
  for (let retry = 0; retry < 2; retry++) {
    try {
      // 从配置中获取域名和邮件服务器域名
      let domain = null;
      let mailServerDomain = null;

      // 从配置中获取域名
      await new Promise((resolve) => {
        chrome.storage.sync.get('augmentCodeConfig', (result) => {
          if (result.augmentCodeConfig) {
            // 随机选择一个域名，而不是使用配置中的固定域名
            domain = getRandomEmailDomain();
            // 邮件服务器域名从 tempEmailApi 中提取
            const tempEmailApi = result.augmentCodeConfig.tempEmailApi || '';
            // 从 URL 中提取域名部分
            const urlMatch = tempEmailApi.match(/https?:\/\/([^/]+)/);
            mailServerDomain = urlMatch ? urlMatch[1].replace('mail1.', 'mail.') : 'mail.niubea.site';
          }
          resolve();
        });
      });

      // 如果无法从配置中获取，使用随机域名
      domain = domain || getRandomEmailDomain();
      mailServerDomain = mailServerDomain || 'mail.niubea.site';

      // 创建邮箱API地址 - 新API地址
      const createEmailUrl = `https://${mailServerDomain}/api/new_address`;

      // 添加日志
      log(`创建邮箱: name=${emailName}, domain=${domain}, API=${createEmailUrl}`);

      // 生成随机密码
      const generateRandomPassword = (length = 12) => {
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+";
        let password = "";
        for (let i = 0; i < length; i++) {
          const randomIndex = Math.floor(Math.random() * charset.length);
          password += charset[randomIndex];
        }
        return password;
      };

      // 生成一个随机密码，而不是使用JWT作为密码
      const randomPassword = generateRandomPassword(12);

      // 使用新的API请求格式
      const response = await fetch(createEmailUrl, {
        method: 'POST',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'accept-language': 'zh-CN,zh;q=0.9',
          'authorization': 'Bearer',
          'cache-control': 'no-cache',
          'content-type': 'application/json',
          'pragma': 'no-cache',
          'priority': 'u=1, i',
          'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-site',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
          'x-admin-auth': '',
          'x-custom-auth': '',
          'x-lang': 'zh',
          'x-user-token': ''
        },
        body: JSON.stringify({
          "name": emailName,
          "domain": domain,
          "cf_token": ""
        }),
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        timeout: 30000
      });

      if (!response.ok) {
        if (retry === 1) throw new Error(`创建邮箱失败: ${response.status}`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
        continue;
      }

      const data = await response.json();
      log(`创建邮箱响应: ${JSON.stringify(data)}`);

      // 保存新创建的邮箱信息到存储中
      await new Promise((resolve) => {
        chrome.storage.local.set({ createdEmail: data.address || `${emailName}@${domain}` }, () => {
          log(`保存了新创建的邮箱: ${data.address || `${emailName}@${domain}`}`);
          resolve();
        });
      });

      // 保存jwt令牌以供后续使用
      if (data.jwt) {
        await new Promise((resolve) => {
          chrome.storage.local.set({ emailJwt: data.jwt }, () => {
            log(`保存了JWT令牌用于邮件获取: ${data.jwt.substring(0, 15)}...`);
            resolve();
          });
        });
      } else {
        log(`警告：API返回数据中没有JWT令牌，邮件获取可能会失败`);
        // 尝试从响应中其他位置获取JWT
        if (data.token) {
          await new Promise((resolve) => {
            chrome.storage.local.set({ emailJwt: data.token }, () => {
              log(`从token字段保存了JWT令牌: ${data.token.substring(0, 15)}...`);
              resolve();
            });
          });
        }
      }

      // 保存临时邮箱账户信息，包括邮箱地址、随机密码和JWT令牌
      const emailAddress = data.address || `${emailName}@${domain}`;
      await new Promise((resolve) => {
        chrome.storage.local.set({
          tempMailAccount: {
            email: emailAddress,
            password: randomPassword,
            jwt: data.jwt || data.token || "",
            createdAt: new Date().toISOString()
          }
        }, () => {
          log(`保存了临时邮箱账户信息: ${emailAddress}`);
          resolve();
        });
      });

      return {
        success: true,
        data,
        email: data.address || `${emailName}@${domain}`,
        password: randomPassword // 使用生成的随机密码，而不是JWT令牌
      };
    } catch (error) {
      log(`创建邮箱出错: ${error.message}`);
      if (retry === 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
    }
  }

  throw new Error('创建邮箱失败');
}

// 后台跨域请求邮件列表
async function fetchMailList(apiUrl, username, domain, pin) {
  // 最多重试2次
  for (let retry = 0; retry < 2; retry++) {
    try {
      // 获取配置信息，使用realEmailDomain作为API查询参数
      let apiDomain = domain;

      // 尝试获取配置中的realEmailDomain
      await new Promise((resolve) => {
        chrome.storage.sync.get('augmentCodeConfig', (result) => {
          if (result.augmentCodeConfig && result.augmentCodeConfig.realEmailDomain) {
            apiDomain = result.augmentCodeConfig.realEmailDomain;
          }
          resolve();
        });
      });

      // 从配置中获取邮件服务器域名
      let mailServerDomain = null;
      await new Promise((resolve) => {
        chrome.storage.sync.get('augmentCodeConfig', (result) => {
          if (result.augmentCodeConfig) {
            // 从 tempEmailApi 中提取邮件服务器域名
            const tempEmailApi = result.augmentCodeConfig.tempEmailApi || '';
            const urlMatch = tempEmailApi.match(/https?:\/\/([^/]+)/);
            mailServerDomain = urlMatch ? urlMatch[1].replace('mail1.', 'mail.') : 'mail.niubea.site';
          }
          resolve();
        });
      });
      mailServerDomain = mailServerDomain || 'mail.niubea.site';

      // 新的邮件列表获取API
      const mailListUrl = `https://${mailServerDomain}/api/mails?limit=20&offset=0`;

      // 获取之前存储的JWT令牌
      let jwt = null;
      await new Promise((resolve) => {
        chrome.storage.local.get('emailJwt', (result) => {
          jwt = result.emailJwt || '';
          resolve();
        });
      });

      // 添加日志，打印实际请求地址
      log(`请求邮件列表的API地址: ${mailListUrl}, JWT: ${jwt ? '已获取' : '未找到'}`);

      const response = await fetch(mailListUrl, {
        method: 'GET',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'accept-language': 'zh-CN,zh;q=0.9',
          'authorization': jwt ? `Bearer ${jwt}` : 'Bearer',
          'cache-control': 'no-cache',
          'content-type': 'application/json',
          'pragma': 'no-cache',
          'priority': 'u=1, i',
          'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-site',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
          'x-admin-auth': '',
          'x-custom-auth': '',
          'x-lang': 'zh',
          'x-user-token': ''
        },
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        timeout: 30000
      });

      if (!response.ok) {
        if (retry === 1) throw new Error(`请求失败: ${response.status}`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
        continue;
      }

      const data = await response.json();
      log(`获取邮件列表响应: ${JSON.stringify(data)}`);

      // 适配新API返回格式到旧格式
      const adaptedData = {
        result: true,
        mails: data.items || [],
        first_id: data.items && data.items.length > 0 ? data.items[0].id : null
      };

      return adaptedData;
    } catch (error) {
      log(`获取邮件列表出错: ${error.message}`);
      if (retry === 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
    }
  }

  throw new Error('无法获取邮件列表');
}

// 后台跨域请求邮件详情
async function fetchMailDetail(apiUrl, mailId, username, domain, pin) {
  // 最多重试2次
  for (let retry = 0; retry < 2; retry++) {
    try {
      // 获取配置信息，使用realEmailDomain作为API查询参数
      let apiDomain = domain;

      // 尝试获取配置中的realEmailDomain
      await new Promise((resolve) => {
        chrome.storage.sync.get('augmentCodeConfig', (result) => {
          if (result.augmentCodeConfig && result.augmentCodeConfig.realEmailDomain) {
            apiDomain = result.augmentCodeConfig.realEmailDomain;
          }
          resolve();
        });
      });

      // 从配置中获取邮件服务器域名
      let mailServerDomain = null;
      await new Promise((resolve) => {
        chrome.storage.sync.get('augmentCodeConfig', (result) => {
          if (result.augmentCodeConfig) {
            // 从 tempEmailApi 中提取邮件服务器域名
            const tempEmailApi = result.augmentCodeConfig.tempEmailApi || '';
            const urlMatch = tempEmailApi.match(/https?:\/\/([^/]+)/);
            mailServerDomain = urlMatch ? urlMatch[1].replace('mail1.', 'mail.') : 'mail.niubea.site';
          }
          resolve();
        });
      });
      mailServerDomain = mailServerDomain || 'mail.niubea.site';

      // 新的邮件详情获取API
      const mailDetailUrl = `https://${mailServerDomain}/api/mail/${mailId}`;

      // 获取之前存储的JWT令牌
      let jwt = null;
      await new Promise((resolve) => {
        chrome.storage.local.get('emailJwt', (result) => {
          jwt = result.emailJwt || '';
          resolve();
        });
      });

      // 添加日志，打印实际请求地址
      log(`请求邮件详情的API地址: ${mailDetailUrl}, JWT: ${jwt ? '已获取' : '未找到'}`);

      const response = await fetch(mailDetailUrl, {
        method: 'GET',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'accept-language': 'zh-CN,zh;q=0.9',
          'authorization': jwt ? `Bearer ${jwt}` : 'Bearer',
          'cache-control': 'no-cache',
          'content-type': 'application/json',
          'pragma': 'no-cache',
          'priority': 'u=1, i',
          'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-site',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
          'x-admin-auth': '',
          'x-custom-auth': '',
          'x-lang': 'zh',
          'x-user-token': ''
        },
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        timeout: 30000
      });

      if (!response.ok) {
        if (retry === 1) throw new Error(`请求失败: ${response.status}`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
        continue;
      }

      const data = await response.json();
      log(`获取邮件详情响应: ${JSON.stringify(data)}`);

      // 适配新API返回格式到旧格式
      const adaptedData = {
        result: true,
        id: data.id,
        from: data.fromAddress,
        to: data.toAddress,
        subject: data.subject,
        date: data.date,
        text: data.textBody || data.htmlBody,
        html: data.htmlBody
      };

      // 提取验证码
      const mailText = adaptedData.text || '';
      const codeMatch = mailText.match(/(?<![a-zA-Z@.])\b\d{6}\b/);

      if (codeMatch) {
        adaptedData.verificationCode = codeMatch[0];
      }

      return adaptedData;
    } catch (error) {
      log(`获取邮件详情出错: ${error.message}`);
      if (retry === 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
    }
  }

  throw new Error('无法获取邮件详情');
}

// 后台跨域删除邮件
async function deleteMailById(apiUrl, mailId, username, domain, pin) {
  // 最多重试3次
  for (let retry = 0; retry < 3; retry++) {
    try {
      const deleteUrl = `${apiUrl}/api/mails/`;

      const formData = new URLSearchParams();
      formData.append('email', `${username}@${domain}`);
      formData.append('first_id', mailId);
      formData.append('epin', pin || '');

      log(`尝试删除邮件: ID=${mailId}, 邮箱=${username}@${domain}`);

      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        body: formData,
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        timeout: 10000
      });

      if (!response.ok) {
        if (retry === 2) throw new Error(`请求失败: ${response.status}`);
        await new Promise(resolve => setTimeout(resolve, 200));
        continue;
      }

      const data = await response.json();

      if (data && data.result === true) {
        log(`邮件删除成功: ${mailId}`);
        return { success: true };
      } else {
        if (retry === 2) throw new Error('API返回失败结果');
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    } catch (error) {
      if (retry === 2) throw error;
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  throw new Error('无法删除邮件');
}

// 后台处理PKCE深度登录的API请求
async function fetchPKCEToken(authPollUrl) {
  // 最多重试3次
  for (let retry = 0; retry < 3; retry++) {
    try {
      log(`尝试获取PKCE令牌: ${authPollUrl}`);

      const response = await fetch(authPollUrl, {
        method: 'GET',
        headers: {
          'Accept': '*/*',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cursor/0.48.6 Chrome/132.0.6834.210 Electron/34.3.4 Safari/537.36'
        },
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        timeout: 15000
      });

      if (!response.ok) {
        log(`PKCE令牌请求失败: ${response.status}`);
        if (retry === 2) throw new Error(`请求失败: ${response.status}`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
        continue;
      }

      const data = await response.json();
      log(`PKCE令牌响应: `, data);

      if (data && data.accessToken) {
        log(`成功获取PKCE令牌`);
        return data;
      } else {
        log(`PKCE令牌响应中无accessToken`);
        if (retry === 2) throw new Error('API返回中未找到accessToken');
        await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
      }
    } catch (error) {
      log(`PKCE令牌请求异常: ${error.message}`);
      if (retry === 2) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
    }
  }

  throw new Error('无法获取PKCE令牌');
}

// 从niubea.site邮箱获取验证码
async function getMailVerificationCode(email) {
  try {
    log(`尝试获取邮箱验证码: ${email}`);

    // 获取JWT令牌
    let jwt = null;
    await new Promise((resolve) => {
      chrome.storage.local.get('emailJwt', (result) => {
        jwt = result.emailJwt || '';
        resolve();
      });
    });

    log(`JWT状态: ${jwt ? '已获取' : '未找到'}`);

    if (!jwt) {
      return { error: '未找到JWT令牌，无法获取邮件' };
    }

    // 使用API获取邮件列表 - 不再拼接邮箱地址
    const apiUrl = 'https://mail.niubea.site/api/mails?limit=20&offset=0';
    log(`获取邮件列表，API: ${apiUrl}`);

    try {
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Authorization': `Bearer ${jwt}`,
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          'Pragma': 'no-cache',
          'Priority': 'u=1, i',
          'Sec-CH-UA': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'Sec-CH-UA-Mobile': '?0',
          'Sec-CH-UA-Platform': '"Windows"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-site',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
          'X-Admin-Auth': '',
          'X-Custom-Auth': '',
          'X-Lang': 'zh',
          'X-User-Token': ''
        }
      });

      if (!response.ok) {
        error(`API响应错误: ${response.status}`);
        return { error: `API响应错误: ${response.status}` };
      }

      const mailData = await response.json();
      log(`API响应: ${JSON.stringify(mailData).substring(0, 200)}...`);

      // 适配新旧API格式
      const emails = mailData.items || mailData.results || [];

      if (!emails || !Array.isArray(emails) || emails.length === 0) {
        log(`未找到任何邮件`);
        return { error: '未找到任何邮件' };
      }

      // 查找最新的邮件
      const latestEmail = emails[0];

      if (!latestEmail || !latestEmail.id) {
        log(`未找到有效的邮件ID`);
        return { error: '未找到有效的邮件' };
      }

      // 获取邮件详情
      const mailDetailUrl = `https://mail.niubea.site/api/mail/${latestEmail.id}`;
      log(`获取邮件详情，API: ${mailDetailUrl}`);

      const detailResponse = await fetch(mailDetailUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Authorization': `Bearer ${jwt}`,
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          'Origin': 'https://mail.niubea.site',
          'Pragma': 'no-cache',
          'Priority': 'u=1, i',
          'Referer': 'https://mail.niubea.site/',
          'Sec-CH-UA': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'Sec-CH-UA-Mobile': '?0',
          'Sec-CH-UA-Platform': '"Windows"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-site',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
          'X-Admin-Auth': '',
          'X-Custom-Auth': '',
          'X-Lang': 'zh',
          'X-User-Token': ''
        }
      });

      if (!detailResponse.ok) {
        error(`获取邮件详情失败: ${detailResponse.status}`);
        return { error: `获取邮件详情失败: ${detailResponse.status}` };
      }

      const mailDetail = await detailResponse.json();

      // 获取邮件内容
      const rawContent = mailDetail.raw || '';
      const htmlContent = mailDetail.htmlBody || '';
      const textContent = mailDetail.textBody || '';

      // 尝试提取验证码 - 使用多种提取模式

      // 提取模式1: 查找"Enter the code below"后跟随的6位数字
      const enterCodePattern = /Enter the code below[\s\S]*?(\d{6})[\s\S]*?expires/i;
      let match = enterCodePattern.exec(rawContent) || enterCodePattern.exec(htmlContent) || enterCodePattern.exec(textContent);

      if (match && match[1]) {
        const code = match[1];
        log(`从"Enter the code below"模式中找到验证码: ${code}`);
        return { verifyCode: code };
      }

      // 提取模式2: 标准6位数字验证码
      const sixDigitPattern = /\b(\d{6})\b/;
      match = sixDigitPattern.exec(rawContent) || sixDigitPattern.exec(htmlContent) || sixDigitPattern.exec(textContent);

      if (match && match[1]) {
        const code = match[1];
        log(`找到6位数字验证码: ${code}`);
        return { verifyCode: code };
      }

      // 提取模式3: 其他常见验证码格式
      const otherVerificationPatterns = [
        /verification\s+code\s*[:\s]\s*(\d+)/i,
        /verification\s+code\s*[:\s]\s*([A-Z0-9]+)/i,
        /验证码\s*[：:]\s*(\d+)/i,
        /code[:\s]*\s*(\d+)/i
      ];

      for (const pattern of otherVerificationPatterns) {
        match = pattern.exec(rawContent) || pattern.exec(htmlContent) || pattern.exec(textContent);
        if (match && match[1]) {
          const code = match[1];
          log(`使用模式 ${pattern} 找到验证码: ${code}`);
          return { verifyCode: code };
        }
      }

      // 如果所有模式都未匹配，尝试提取任何4-6位数字
      const anyDigitsPattern = /\b(\d{4,6})\b/;
      match = anyDigitsPattern.exec(rawContent) || anyDigitsPattern.exec(htmlContent) || anyDigitsPattern.exec(textContent);

      if (match && match[1]) {
        const code = match[1];
        log(`提取到可能的验证码: ${code}`);
        return { verifyCode: code };
      }

      log('未能从邮件中提取验证码');
      return { error: '未能从邮件中提取验证码' };

    } catch (err) {
      error(`获取邮件失败: ${err.message}`);
      return { error: `获取邮件失败: ${err.message}` };
    }
  } catch (err) {
    error(`验证码获取过程中发生错误: ${err.message}`);
    return { error: err.message };
  }
}

// 直接通过API获取邮件列表，不需要username和domain参数
async function fetchMailsDirectly(apiUrl, jwt) {
  // 最多重试2次
  for (let retry = 0; retry < 3; retry++) {
    try {
      // 确保API URL格式正确
      if (!apiUrl.startsWith('http')) {
        apiUrl = 'https://' + apiUrl;
      }

      // 处理URL末尾的斜杠
      if (!apiUrl.endsWith('/')) {
        apiUrl = apiUrl + '/';
      }

      // 构建邮件列表请求URL
      const mailListUrl = `${apiUrl}api/mails?limit=20&offset=0`;

      log(`直接获取邮件列表: ${mailListUrl}, JWT: ${jwt ? '已提供' : '未提供'}`);

      // 构建请求头
      const headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        'x-admin-auth': '',
        'x-custom-auth': '',
        'x-lang': 'zh',
        'x-user-token': ''
      };

      // 如果提供了JWT，添加到授权头
      if (jwt) {
        headers['authorization'] = `Bearer ${jwt}`;
      } else {
        // 如果没有提供JWT，尝试从存储中获取
        try {
          const storedJwt = await new Promise((resolve) => {
            chrome.storage.local.get('emailJwt', (result) => {
              resolve(result.emailJwt || '');
            });
          });

          if (storedJwt) {
            headers['authorization'] = `Bearer ${storedJwt}`;
            log(`使用存储的JWT令牌`);
          } else {
            headers['authorization'] = 'Bearer';
          }
        } catch (e) {
          headers['authorization'] = 'Bearer';
          log(`获取JWT令牌失败: ${e.message}`);
        }
      }

      const response = await fetch(mailListUrl, {
        method: 'GET',
        headers: headers,
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        timeout: 30000
      });

      if (!response.ok) {
        if (retry === 1) throw new Error(`请求失败: ${response.status}`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
        continue;
      }

      const data = await response.json();

      // 检查并处理不同的API响应格式（items或results字段）
      const mailItems = data.items || data.results || [];

      log(`获取邮件列表成功，共 ${mailItems.length} 封`);

      // 返回一个包含所有必要信息的对象，而不仅仅是邮件数组
      return {
        success: true,
        data: {
          items: mailItems,
          results: mailItems, // 同时提供results字段以确保兼容性
          count: data.count || mailItems.length
        }
      };
    } catch (error) {
      log(`获取邮件列表出错: ${error.message}`);
      if (retry === 2) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
    }
  }

  throw new Error('无法获取邮件列表');
}

// 直接通过API获取邮件详情，不需要username和domain参数
async function fetchMailDetailDirectly(apiUrl, mailId, jwt) {
  // 最多重试2次
  for (let retry = 0; retry < 2; retry++) {
    try {
      // 确保API URL格式正确
      if (!apiUrl.startsWith('http')) {
        apiUrl = 'https://' + apiUrl;
      }

      // 处理URL末尾的斜杠
      if (!apiUrl.endsWith('/')) {
        apiUrl = apiUrl + '/';
      }

      // 构建邮件详情请求URL
      const mailDetailUrl = `${apiUrl}api/mail/${mailId}`;

      log(`直接获取邮件详情: ${mailDetailUrl}, JWT: ${jwt ? '已提供' : '未提供'}`);

      // 构建请求头
      const headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        'x-admin-auth': '',
        'x-custom-auth': '',
        'x-lang': 'zh',
        'x-user-token': ''
      };

      // 如果提供了JWT，添加到授权头
      if (jwt) {
        headers['authorization'] = `Bearer ${jwt}`;
      } else {
        // 如果没有提供JWT，尝试从存储中获取
        try {
          const storedJwt = await new Promise((resolve) => {
            chrome.storage.local.get('emailJwt', (result) => {
              resolve(result.emailJwt || '');
            });
          });

          if (storedJwt) {
            headers['authorization'] = `Bearer ${storedJwt}`;
            log(`使用存储的JWT令牌`);
          } else {
            headers['authorization'] = 'Bearer';
          }
        } catch (e) {
          headers['authorization'] = 'Bearer';
          log(`获取JWT令牌失败: ${e.message}`);
        }
      }

      const response = await fetch(mailDetailUrl, {
        method: 'GET',
        headers: headers,
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        timeout: 30000
      });

      if (!response.ok) {
        if (retry === 1) throw new Error(`请求失败: ${response.status}`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
        continue;
      }

      const data = await response.json();
      log(`获取邮件详情成功: ${data.subject || '无主题'}`);

      // 确保邮件详情包含必要的字段
      const processedData = {
        id: data.id,
        subject: data.subject || '',
        fromAddress: data.fromAddress || data.source || '',
        toAddress: data.toAddress || data.address || '',
        htmlBody: data.htmlBody || '',
        textBody: data.textBody || '',
        raw: data.raw || '',
        created_at: data.created_at || data.createdAt || new Date().toISOString()
      };

      return {
        success: true,
        data: processedData
      };
    } catch (error) {
      log(`获取邮件详情出错: ${error.message}`);
      if (retry === 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
    }
  }

  throw new Error('无法获取邮件详情');
}

// 监听来自content.js和popup.js的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // 处理窗口移动请求
  if (request.type === "moveWindow" && request.deltaX !== undefined && request.deltaY !== undefined) {
    // 注意：Chrome扩展目前不提供直接移动窗口的API
    // 这是一个占位实现，仅在popup.js中使用CSS实现了拖动功能
    // 如果将来Chrome支持窗口移动API，可以在这里实现
    log(`收到窗口移动请求，但Chrome扩展API不支持窗口移动: deltaX=${request.deltaX}, deltaY=${request.deltaY}`);
    sendResponse({ success: false, message: "Chrome扩展API不支持窗口移动" });
    return true;
  }

  // 处理各种消息类型
  if (request.type === "getConfig") {
    chrome.storage.sync.get('augmentCodeConfig', (result) => {
      sendResponse({ config: result.augmentCodeConfig });
    });
    return true;
  }

  if (request.type === "setConfig") {
    chrome.storage.sync.set({ augmentCodeConfig: request.config }, () => {
      sendResponse({ success: true });
    });
    return true;
  }

  if (request.type === "getAccountInfo") {
    chrome.storage.local.get('augmentCodeAccountInfo', (result) => {
      sendResponse({ accountInfo: result.augmentCodeAccountInfo });
    });
    return true;
  }

  if (request.type === "setAccountInfo") {
    chrome.storage.local.set({ augmentCodeAccountInfo: request.accountInfo }, () => {
      sendResponse({ success: true });
    });
    return true;
  }

  if (request.type === "clearAccountInfo") {
    chrome.storage.local.remove(['augmentCodeAccountInfo', 'augmentCodeToken'], () => {
      sendResponse({ success: true });
    });
    return true;
  }

  if (request.type === "getToken") {
    chrome.storage.local.get('augmentCodeToken', (result) => {
      sendResponse({ augmentCodeToken: result.augmentCodeToken });
    });
    return true;
  }

  if (request.type === "setToken") {
    chrome.storage.local.set({ augmentCodeToken: request.augmentCodeToken }, () => {
      sendResponse({ success: true });
    });
    return true;
  }

  if (request.type === "fetchMailList") {
    if (request.username && request.domain) {
      fetchMailList(request.apiUrl, request.username, request.domain, request.pin)
        .then(data => {
          sendResponse({ success: true, data });
        })
        .catch(error => {
          sendResponse({ success: false, error: error.message });
        });
      return true;
    }
  }

  if (request.type === "fetchMailDetail") {
    if (request.mailId && request.username && request.domain) {
      fetchMailDetail(request.apiUrl, request.mailId, request.username, request.domain, request.pin)
        .then(data => {
          sendResponse({ success: true, data });
        })
        .catch(error => {
          sendResponse({ success: false, error: error.message });
        });
      return true;
    }
  }

  if (request.type === "deleteMailById") {
    if (request.mailId && request.username && request.domain) {
      deleteMailById(request.apiUrl, request.mailId, request.username, request.domain, request.pin)
        .then(result => {
          sendResponse({ success: true });
        })
        .catch(error => {
          sendResponse({ success: false, error: error.message });
        });
      return true;
    }
  }

  if (request.type === "fetchPKCEToken") {
    if (request.authPollUrl) {
      fetchPKCEToken(request.authPollUrl)
        .then(data => {
          sendResponse({ success: true, data });
        })
        .catch(error => {
          sendResponse({ success: false, error: error.message });
        });
      return true;
    }
  }

  // 处理WebDAV连接测试请求
  if (request.type === "testWebDAV") {
    testWebDAVConnection(request, sendResponse);
    return true; // 异步响应
  }

  // 处理WebDAV同步数据请求
  if (request.type === "syncToWebDAV") {
    syncToWebDAV(request, sendResponse);
    return true; // 异步响应
  }

  // 处理从WebDAV获取数据请求
  if (request.type === "getFromWebDAV") {
    getFromWebDAV(request, sendResponse);
    return true; // 异步响应
  }

  // 处理创建WebDAV文件夹的请求
  if (request.type === "createWebDAVFolder") {
    createWebDAVFolder(request, sendResponse);
    return true; // 异步响应
  }

  // 处理创建邮箱请求
  if (request.type === "createEmail") {
    const { apiUrl, emailName } = request;

    if (apiUrl && emailName) {
      createEmail(apiUrl, emailName)
        .then(result => {
          // 自动保存创建的账号信息
          if (result && result.success) {
            const { email, password } = result;
            if (email && password) {
              log(`自动保存创建的账号信息: ${email}`);
              chrome.storage.local.set({ accountInfo: { email, password, savedAt: new Date().toISOString() } });
            }
          }
          
          sendResponse(result);
        })
        .catch(error => {
          sendResponse({ success: false, error: error.message });
        });
    } else {
      sendResponse({ success: false, error: "缺少必要的参数" });
    }

    return true;  // 表示我们会异步返回结果
  }

  // 处理获取动态口令请求
  if (request.type === "getDynamicCode") {
    getDynamicCode()
      .then(result => {
        sendResponse({ success: true, data: result });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  }

  // 处理付费口令验证请求
  if (request.type === "verifyPaymentCode") {
    if (request.userInputCode) {
      verifyPaymentCode(request.userInputCode)
        .then(result => {
          sendResponse(result);
        })
        .catch(error => {
          sendResponse({ success: false, message: error.message });
        });
    } else {
      sendResponse({ success: false, message: '缺少验证口令参数' });
    }
    return true;
  }

  // 处理augkl动态口令验证请求
  if (request.type === "verifyAugklCode") {
    if (request.userInputCode) {
      verifyAugklCode(request.userInputCode)
        .then(result => {
          sendResponse(result);
        })
        .catch(error => {
          sendResponse({ success: false, message: error.message });
        });
    } else {
      sendResponse({ success: false, message: '缺少验证口令参数' });
    }
    return true;
  }

  // 处理邮件验证码获取请求
  if (request.type === "getMailVerifyCode") {
    if (request.email) {
      getMailVerificationCode(request.email)
        .then(result => {
          sendResponse(result);
        })
        .catch(error => {
          sendResponse({ error: error.message });
        });
      return true;
    }
  }

  // 保存账号信息
  if (request.type === "saveAccountInfo") {
    const { email, password } = request;
    if (email && password) {
      log(`保存账号信息: ${email}`);
      chrome.storage.local.set({ accountInfo: { email, password, savedAt: new Date().toISOString() } }, () => {
        sendResponse({ success: true });
      });
      return true;
    } else {
      sendResponse({ success: false, error: "缺少必要的邮箱或密码参数" });
      return true;
    }
  }

  // 获取账号信息
  if (request.type === "getAccountInfo") {
    chrome.storage.local.get('accountInfo', (result) => {
      log(`获取账号信息: ${result.accountInfo ? result.accountInfo.email : '无'}`);
      sendResponse({ success: true, accountInfo: result.accountInfo || {} });
    });
    return true;
  }

  // 直接获取邮件列表
  if (request.type === "fetchMailsDirectly") {
    fetchMailsDirectly(request.apiUrl, request.jwt)
      .then(data => {
        sendResponse({ success: true, data: data });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  }

  // 直接获取邮件详情
  if (request.type === "fetchMailDetailDirectly") {
    fetchMailDetailDirectly(request.apiUrl, request.mailId, request.jwt)
      .then(data => {
        sendResponse({ success: true, data: data });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  }

  // 测试邮箱API
  if (request.type === "testEmailApi") {
    testEmailApi(request.apiUrl, request.email)
      .then(result => {
        sendResponse(result);
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  }

  // 处理新的fetchData请求类型
  if (request.type === 'fetchData') {
    fetchData(request.url, request.options)
      .then(result => {
        sendResponse({ success: true, data: result });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // 异步响应
  }

  // 默认返回
  if (request.type === "notification") {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon128.png',
      title: request.title || 'Cursor 辅助注册助手',
      message: request.message || '',
      priority: 1
    });
    return false;  // 没有异步响应
  }

  return false;
});

// 直接获取数据的通用函数
async function fetchData(url, options = {}) {
  // 最多重试2次
  for (let retry = 0; retry < 2; retry++) {
    try {
      log(`发起请求: URL=${url}, 方法=${options.method || 'GET'}`);
      
      // 获取之前存储的JWT令牌
      let jwt = null;
      await new Promise((resolve) => {
        chrome.storage.local.get('emailJwt', (result) => {
          jwt = result.emailJwt || '';
          resolve();
        });
      });
      
      // 构建请求头
      const defaultHeaders = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'authorization': jwt ? `Bearer ${jwt}` : 'Bearer',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'pragma': 'no-cache',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
      };
      
      // 合并请求头
      const headers = {...defaultHeaders, ...options.headers};
      
      const fetchOptions = {
        method: options.method || 'GET',
        headers: headers,
        body: options.body,
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        timeout: 30000,
        ...options
      };
      
      // 移除undefined属性
      Object.keys(fetchOptions).forEach(key => {
        if (fetchOptions[key] === undefined) {
          delete fetchOptions[key];
        }
      });
      
      const response = await fetch(url, fetchOptions);
      
      if (!response.ok) {
        log(`请求失败: 状态码=${response.status}, URL=${url}`);
        if (retry === 1) throw new Error(`请求失败: ${response.status}`);
        await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
        continue;
      }
      
      // 尝试解析JSON响应
      try {
        const data = await response.json();
        log(`请求成功: URL=${url}, 响应数据=${JSON.stringify(data).substring(0, 100)}...`);
        return data;
      } catch (jsonError) {
        // 如果不是JSON格式，返回文本
        const text = await response.text();
        log(`请求成功但非JSON格式: URL=${url}, 响应文本=${text.substring(0, 100)}...`);
        return text;
      }
    } catch (error) {
      log(`请求出错: ${error.message}, URL=${url}`);
      if (retry === 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (retry + 1)));
    }
  }
  
  throw new Error(`请求失败: ${url}`);
}