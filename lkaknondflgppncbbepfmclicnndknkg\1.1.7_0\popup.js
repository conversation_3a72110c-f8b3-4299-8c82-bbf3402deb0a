// 弹出窗口初始化
document.addEventListener('DOMContentLoaded', () => {
  // 加载配置
  loadConfig();
  // 加载Token信息
  loadTokenInfo();

  // 添加事件监听器
  document.getElementById('saveConfig').addEventListener('click', saveConfig);
  document.getElementById('clearData').addEventListener('click', clearData);
  document.getElementById('copyToken').addEventListener('click', copyToken);
  document.getElementById('goToSignup').addEventListener('click', goToSignup);
});

// 加载配置
function loadConfig() {
  chrome.runtime.sendMessage({ type: "getConfig" }, (response) => {
    if (response && response.config) {
      const config = response.config;

      // 填充表单
      document.getElementById('emailPrefix').value = config.emailPrefix || '';
      document.getElementById('emailDomain').value = config.emailDomain || '';
      document.getElementById('realEmailDomain').value = config.realEmailDomain || '';
      document.getElementById('emailCodeType').value = config.emailCodeType || 'AUTO';
      document.getElementById('emailPin').value = config.emailPin || '';
      document.getElementById('tempEmailApi').value = config.tempEmailApi || 'https://mail.niubea.site';
      document.getElementById('emailVerificationRetries').value = config.emailVerificationRetries || 5;
      document.getElementById('emailVerificationWait').value = config.emailVerificationWait || 10;
      document.getElementById('autoFill').checked = config.autoFill !== false;
    }
  });
}

// 保存配置
function saveConfig() {
  const config = {
    emailPrefix: document.getElementById('emailPrefix').value,
    emailDomain: document.getElementById('emailDomain').value,
    realEmailDomain: document.getElementById('realEmailDomain').value,
    emailCodeType: document.getElementById('emailCodeType').value,
    emailPin: document.getElementById('emailPin').value,
    tempEmailApi: document.getElementById('tempEmailApi').value,
    emailVerificationRetries: parseInt(document.getElementById('emailVerificationRetries').value) || 5,
    emailVerificationWait: parseInt(document.getElementById('emailVerificationWait').value) || 10,
    autoFill: document.getElementById('autoFill').checked
  };

  chrome.runtime.sendMessage({ type: "setConfig", config: config }, (response) => {
    if (response && response.success) {
      showNotification('配置已保存', 'success');
    } else {
      showNotification('保存配置失败', 'error');
    }
  });
}

// 清除账号数据
function clearData() {
  if (confirm('确定要清除当前账号数据吗？这将删除所有保存的账号信息和Token。')) {
    chrome.runtime.sendMessage({ type: "clearData" }, (response) => {
      if (response && response.success) {
        showNotification('账号数据已清除', 'success');
        // 重新加载Token信息
        loadTokenInfo();
      } else {
        showNotification('清除账号数据失败', 'error');
      }
    });
  }
}

// 加载Token信息
function loadTokenInfo() {
  chrome.runtime.sendMessage({ type: "getToken" }, (tokenResponse) => {
    chrome.runtime.sendMessage({ type: "getAccountInfo" }, (accountResponse) => {
      const tokenInfo = tokenResponse && tokenResponse.augmentCodeToken;
      const accountInfo = accountResponse && accountResponse.accountInfo;

      const tokenInfoDiv = document.getElementById('tokenInfo');
      const noTokenInfoDiv = document.getElementById('noTokenInfo');

      if (tokenInfo && tokenInfo.token) {
        // 显示Token信息
        document.getElementById('emailDisplay').textContent = accountInfo ? accountInfo.email : '-';
        document.getElementById('passwordDisplay').textContent = accountInfo ? accountInfo.password : '-';
        document.getElementById('userDisplay').textContent = tokenInfo.user || '-';
        document.getElementById('tokenDisplay').value = tokenInfo.token;

        tokenInfoDiv.classList.remove('hidden');
        noTokenInfoDiv.classList.add('hidden');
      } else {
        // 无Token信息
        tokenInfoDiv.classList.add('hidden');
        noTokenInfoDiv.classList.remove('hidden');
      }
    });
  });
}

// 复制Token
function copyToken() {
  const tokenText = document.getElementById('tokenDisplay').value;
  const accountEmail = document.getElementById('emailDisplay').textContent;
  const accountPassword = document.getElementById('passwordDisplay').textContent;
  const user = document.getElementById('userDisplay').textContent;

  const copyText = `邮箱: ${accountEmail}\n密码: ${accountPassword}\n用户: ${user}\nToken: ${tokenText}`;

  navigator.clipboard.writeText(copyText).then(() => {
    showNotification('账号信息已复制到剪贴板', 'success');
  }).catch(() => {
    // 如果clipboard API失败，使用textarea
    const textarea = document.createElement('textarea');
    textarea.value = copyText;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);

    showNotification('账号信息已复制到剪贴板', 'success');
  });
}

// 前往注册页面
function goToSignup() {
  chrome.tabs.create({ url: 'https://login.augmentcode.com/' });
}

// 显示通知
function showNotification(message, type = 'info') {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    bottom: 10px;
    left: 10px;
    right: 10px;
    padding: 10px;
    border-radius: 4px;
    color: white;
    text-align: center;
    z-index: 1000;
    font-weight: bold;
  `;

  // 根据类型设置背景色
  if (type === 'success') {
    notification.style.backgroundColor = '#4CAF50';
  } else if (type === 'error') {
    notification.style.backgroundColor = '#f44336';
  } else {
    notification.style.backgroundColor = '#2196F3';
  }

  notification.textContent = message;

  // 添加到页面
  document.body.appendChild(notification);

  // 3秒后删除
  setTimeout(() => {
    document.body.removeChild(notification);
  }, 3000);
}
